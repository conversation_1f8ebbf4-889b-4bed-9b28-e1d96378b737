<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lift_group_device_davhars', function (Blueprint $table) {
            $table->integer('floor_no')->nullable()->after('offset');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lift_group_device_davhars', function (Blueprint $table) {
            $table->dropColumn('floor_no');
        });
    }
};
