<?php

namespace App\Services;

use App\Models\LiftGroupDeviceDavhar;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

class LiftGroupDeviceDavharSyncService
{
    protected CvSecurityServiceExt $cvSecurityService;

    public function __construct(CvSecurityServiceExt $cvSecurityService)
    {
        $this->cvSecurityService = $cvSecurityService;
    }

    /**
     * Sync LiftGroupDeviceDavhar creation with CVSecurity service
     */
    public function syncCreate(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during LiftGroupDeviceDavhar creation', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'lift_group_device_id' => $liftGroupDeviceDavhar->lift_group_device_id,
                    'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
                ]);
                $this->updateLiftGroupDeviceDavharCode($liftGroupDeviceDavhar, null);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareLiftGroupDeviceDavharData($liftGroupDeviceDavhar);

            // Call CVSecurity create EleLevel endpoint
            $response = $this->cvSecurityService->createEleLevel($data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateLiftGroupDeviceDavharCode($liftGroupDeviceDavhar, $code);
                Log::info('LiftGroupDeviceDavhar successfully synced with CVSecurity on creation', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $code,
                ]);
            } else {
                $this->updateLiftGroupDeviceDavharCode($liftGroupDeviceDavhar, null);
                Log::error('CVSecurity create operation failed for LiftGroupDeviceDavhar', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            $this->updateLiftGroupDeviceDavharCode($liftGroupDeviceDavhar, null);
            Log::error('Exception during LiftGroupDeviceDavhar CVSecurity sync on creation', [
                'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync LiftGroupDeviceDavhar update with CVSecurity service
     */
    public function syncUpdate(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during LiftGroupDeviceDavhar update', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'lift_group_id' => $liftGroupDeviceDavhar->lift_group_id,
                    'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
                ]);

                return;
            }

            // Check if enabled field was changed - if so, sync with CVSecurity floor
            if ($liftGroupDeviceDavhar->wasChanged('enabled')) {
                Log::info('LiftGroupDeviceDavhar enabled state changed, syncing with CVSecurity floor', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'enabled' => $liftGroupDeviceDavhar->enabled,
                ]);

                $this->syncEnabledStateWithCVSecurityFloor($liftGroupDeviceDavhar);
            }

            // If no code exists, create instead of update
            if (! $liftGroupDeviceDavhar->code) {
                Log::info('No CVSecurity code found for LiftGroupDeviceDavhar, creating instead of updating', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                ]);
                $this->syncCreate($liftGroupDeviceDavhar);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareLiftGroupDeviceDavharData($liftGroupDeviceDavhar);

            // Call CVSecurity update EleLevel endpoint
            $response = $this->cvSecurityService->updateEleLevel($liftGroupDeviceDavhar->code, $data);

            if ($response) {
                Log::info('LiftGroupDeviceDavhar successfully synced with CVSecurity on update', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $liftGroupDeviceDavhar->code,
                ]);
            } else {
                Log::error('CVSecurity update operation failed for LiftGroupDeviceDavhar', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $liftGroupDeviceDavhar->code,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during LiftGroupDeviceDavhar CVSecurity sync on update', [
                'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync LiftGroupDeviceDavhar deletion with CVSecurity service
     */
    public function syncDelete(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during LiftGroupDeviceDavhar deletion', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'lift_group_id' => $liftGroupDeviceDavhar->lift_group_id,
                    'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
                ]);

                return;
            }

            // If no code exists, nothing to delete from CVSecurity
            if (! $liftGroupDeviceDavhar->code) {
                Log::info('No CVSecurity code found for LiftGroupDeviceDavhar, skipping deletion', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                ]);

                return;
            }

            // Call CVSecurity delete EleLevel endpoint
            $response = $this->cvSecurityService->deleteEleLevel($liftGroupDeviceDavhar->code);

            if ($response) {
                Log::info('LiftGroupDeviceDavhar successfully deleted from CVSecurity', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $liftGroupDeviceDavhar->code,
                ]);
            } else {
                Log::error('CVSecurity delete operation failed for LiftGroupDeviceDavhar', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $liftGroupDeviceDavhar->code,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during LiftGroupDeviceDavhar CVSecurity sync on deletion', [
                'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Prepare LiftGroupDeviceDavhar data for CVSecurity API
     */
    private function prepareLiftGroupDeviceDavharData(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): array
    {
        // Load necessary relationships
        $liftGroupDeviceDavhar->load(['liftGroup', 'davhar', 'davhar.korpus']);

        $liftGroupName = $liftGroupDeviceDavhar->liftGroup->name;
        $floorNumber = $liftGroupDeviceDavhar->davhar->number ?? 'Unknown';
        $areaCode = $liftGroupDeviceDavhar->liftGroup->orc->code ?? null;

        $name = "{$liftGroupName} - Floor {$floorNumber}";

        return [
            'name' => $name,
            'area_code' => $areaCode,
        ];
    }

    /**
     * Extract id from CVSecurity response and use it as code
     *
     * @param  mixed  $response
     */
    private function extractCodeFromResponse($response): ?string
    {
        // Check if response indicates success
        if (is_object($response) && isset($response->code) && $response->code === 0 && isset($response->message) && $response->message === 'success') {
            // Extract id from response data
            if (isset($response->data) && isset($response->data->id)) {
                return (string) $response->data->id;
            }
        }

        // Check for direct id field in response
        if (is_object($response) && isset($response->id)) {
            return (string) $response->id;
        }

        // Check for id in data field
        if (is_object($response) && isset($response->data) && isset($response->data->id)) {
            return (string) $response->data->id;
        }

        // Try array format
        if (is_array($response) && isset($response['id'])) {
            return (string) $response['id'];
        }

        if (is_array($response) && isset($response['data']['id'])) {
            return (string) $response['data']['id'];
        }

        // Try to decode JSON response
        if (is_string($response)) {
            $decoded = json_decode($response, true);
            if (is_array($decoded) && isset($decoded['id'])) {
                return (string) $decoded['id'];
            }
            if (is_array($decoded) && isset($decoded['data']['id'])) {
                return (string) $decoded['data']['id'];
            }
        }

        // If response format is different, log it for debugging
        Log::debug('LiftGroupDeviceDavharSyncService: Unexpected response format', [
            'response' => $response,
        ]);

        return null;
    }

    /**
     * Update LiftGroupDeviceDavhar code field
     */
    private function updateLiftGroupDeviceDavharCode(LiftGroupDeviceDavhar $liftGroupDeviceDavhar, ?string $code): void
    {
        // Update without triggering observers to avoid infinite loops
        $liftGroupDeviceDavhar->updateQuietly([LiftGroupDeviceDavhar::CODE => $code]);
    }

    /**
     * Sync LiftGroupDeviceDavhar read with CVSecurity service (optional)
     */
    public function syncRead(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                return;
            }

            // If no code exists, nothing to read from CVSecurity
            if (! $liftGroupDeviceDavhar->code) {
                return;
            }

            // Call CVSecurity get EleLevel endpoint
            $response = $this->cvSecurityService->getEleLevelByCode($liftGroupDeviceDavhar->code);

            if ($response) {
                Log::debug('LiftGroupDeviceDavhar successfully read from CVSecurity', [
                    'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                    'cv_code' => $liftGroupDeviceDavhar->code,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during LiftGroupDeviceDavhar CVSecurity sync on read', [
                'lift_group_davhar_id' => $liftGroupDeviceDavhar->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Synchronize the enabled state of LiftGroupDeviceDavhar with corresponding CVSecurity floor
     */
    public function syncEnabledStateWithCVSecurityFloor(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        try {
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service not available, skipping enabled state sync:', [
                    'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
                ]);

                return;
            }

            // Load necessary relationships
            $liftGroupDeviceDavhar->load(['liftGroupDevice', 'davhar']);

            $deviceId = $liftGroupDeviceDavhar->liftGroupDevice->device_id;
            $davhar = $liftGroupDeviceDavhar->davhar;
            $offset = $liftGroupDeviceDavhar->offset ?? 0;
            // Find the floor that should match this davhar using CORRECT logic: CVSecurity floor_no + offset = davhar.number
            // So expectedFloorNo = davhar.number - offset (to find which CVSecurity floor we need)
            $expectedFloorNo = $davhar->number - $offset;

            $deviceList = $this->cvSecurityService->getDeviceList() ?: [];

            // Find the device and the corresponding floor
            foreach ($deviceList as $device) {
                if ($device['id'] == $deviceId) {
                    foreach ($device['floors'] ?? [] as $floor) {
                        if (isset($floor['floor_no'], $floor['id']) && $floor['floor_no'] === $expectedFloorNo) {
                            // Update CVSecurity floor to match our enabled state
                            $floorData = [
                                [
                                    'id' => $floor['id'],
                                    'enabled' => $liftGroupDeviceDavhar->enabled ?? true,
                                ],
                            ];

                            $result = $this->cvSecurityService->updateFloorStatusBatch($floorData);

                            Log::info('LiftGroupDeviceDavharSyncService: Synchronized enabled state with CVSecurity floor:', [
                                'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
                                'floor_id' => $floor['id'],
                                'floor_no' => $expectedFloorNo,
                                'enabled' => $liftGroupDeviceDavhar->enabled ?? true,
                                'sync_result' => $result,
                            ]);

                            return;
                        }
                    }
                }
            }

            Log::warning('LiftGroupDeviceDavharSyncService: Could not find matching CVSecurity floor for synchronization:', [
                'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
                'device_id' => $deviceId,
                'expected_floor_no' => $expectedFloorNo,
            ]);

        } catch (\Exception $e) {
            Log::error('LiftGroupDeviceDavharSyncService: Exception during CVSecurity enabled state synchronization:', [
                'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
