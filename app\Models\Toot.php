<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Toot
 *
 * @property int $id
 * @property int $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Toot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot query()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Toot extends Model
{
    use HasDeleteProtection, HasFactory;

    const ID = 'id';

    const KORPUS_ID = 'korpus_id';

    const DAVHAR_ID = 'davhar_id';

    const ORC_ID = 'orc_id';

    const NUMBER = 'number';

    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::DAVHAR_ID,
        self::ORC_ID,
        self::NUMBER,
    ];

    public function korpus()
    {
        return $this->belongsTo(Korpus::class);
    }

    public function davhar()
    {
        return $this->belongsTo(Davhar::class);
    }

    public function orc()
    {
        return $this->belongsTo(Orc::class);
    }

    public function liftGroupDavharToots()
    {
        return $this->hasMany(LiftGroupDavharToot::class);
    }

    public function liftGroups()
    {
        return LiftGroup::whereIn('id', function ($query) {
            $query->select('lift_group_device_id')
                ->from('lift_group_device_davhars')
                ->join('lift_group_davhar_toots', 'lift_group_device_davhars.id', '=', 'lift_group_davhar_toots.lift_group_davhar_id')
                ->where('lift_group_davhar_toots.toot_id', $this->id);
        });
    }
}
