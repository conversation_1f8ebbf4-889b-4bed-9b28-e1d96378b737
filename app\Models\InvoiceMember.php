<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\InvoiceMember
 *
 * @property int $id
 * @property int $invoice_id
 * @property int $orshin_suugch_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Invoice|null $invoice
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 *
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember query()
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvoiceMember whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class InvoiceMember extends Model
{
    use HasFactory;

    const ID = 'id';

    const INVOICE_ID = 'invoice_id';

    const ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    protected $fillable = [
        self::ID,
        self::INVOICE_ID,
        self::ORSHIN_SUUGCH_ID,
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }
}
