<?php

namespace App\Filament\Resources\Admin\OrcResource\Pages;

use App\Filament\Resources\Admin\OrcResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOrcs extends ListRecords
{
    protected static string $resource = OrcResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            '#' => 'Орцууд',
        ];
    }
}
