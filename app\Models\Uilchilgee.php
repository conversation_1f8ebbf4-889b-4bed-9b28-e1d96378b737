<?php

namespace App\Models;

use App\Enums\UilchilgeeTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Uilchilgee
 *
 * @property int $id
 * @property string $name
 * @property UilchilgeeTypeEnum $type
 * @property string|null $price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\NehemjlehTohirgoo> $nehemjleh_tohirgoo
 * @property-read int|null $nehemjleh_tohirgoo_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee query()
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Uilchilgee whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Uilchilgee extends Model
{
    use HasFactory;

    const ID = 'id';

    const NAME = 'name';

    const TYPE = 'type';

    const PRICE = 'price';

    const RELATION_NEHEMJLEH_TOHIRGOO = 'nehemjleh_tohirgoo';

    protected $fillable = [
        self::NAME,
        self::TYPE,
        self::PRICE,
    ];

    protected $casts = [
        'type' => UilchilgeeTypeEnum::class,
    ];

    public function nehemjleh_tohirgoo(): BelongsToMany
    {
        return $this->BelongsToMany(NehemjlehTohirgoo::class, 'nehemjleh_tohirgoo_uilchilgee');
    }
}
