<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Bair extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'sukh' => $this->sukh ? new Sukh($this->sukh) : null,
            'aimag' => $this->aimag ? new Aimag($this->aimag) : null,
            'soum' => $this->soum ? new Soum($this->soum) : null,
            'bag' => $this->bag ? new Bag($this->bag) : null,
        ];
    }
}
