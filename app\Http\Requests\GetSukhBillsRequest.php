<?php

namespace App\Http\Requests;

use App\Services\OrshinSuugchService;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetSukhBillsRequest extends FormRequest
{
    const PARAMETER_ORSHIN_SUUGCH_TOOT_ID = 'id';

    public function rules(): array
    {
        $user = auth()->user();
        $orshinSuugch = resolve(OrshinSuugchService::class)->getOrshinSuugchByPhone($user->phone);

        return [
            'id' => [
                'required',
                Rule::exists('orshin_suugch_toots')->where(function (Builder $query) use ($orshinSuugch) {
                    $query->where('orshin_suugch_id', $orshinSuugch->id);
                }),
            ],
        ];
    }
}
