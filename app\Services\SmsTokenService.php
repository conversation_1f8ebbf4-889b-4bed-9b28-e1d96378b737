<?php

namespace App\Services;

use App\Exceptions\SystemException;
use App\Jobs\SendCodeSms;
use App\Models\Constant\ConstData;
use App\Models\SmsToken;

class SmsTokenService
{
    public function sendCodeSms($phone)
    {
        SmsToken::where(SmsToken::PHONE, $phone)->delete();
        $code = rand(1000, 9999);
        $appName = config('app.name');
        $text = "$appName: Batalgaajuulakh kod: $code.";
        SmsToken::create([
            SmsToken::PHONE => $phone,
            SmsToken::CODE => $code,
            SmsToken::EXPIRED_AT => now()->addMinutes(1),
        ]);
        SendCodeSms::dispatch($phone, $text);
    }

    public function checkSmsTokenAfterSend($phone, $code)
    {
        $smstoken = SmsToken::where(SmsToken::PHONE, $phone)->where(SmsToken::CODE, $code)->orderByDesc(SmsToken::CREATED_AT)->first();
        if (! isset($smstoken)) {
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 6);
        } elseif ($smstoken->expired_at < now()) {
            throw new SystemException(ConstData::AUTH_EXCEPTION, 3);
        }

        return $smstoken;
    }
}
