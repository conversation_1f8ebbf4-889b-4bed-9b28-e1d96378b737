<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class OpenDoorRequest extends FormRequest
{
    const PARAMETER_KORPUS_ID = 'korpus_id';

    const PARAMETER_NUMBER = 'number';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'korpus_id' => 'required|exists:korpuses,id',
            'number' => [
                'required',
                Rule::exists('orshin_suugch_toots')->where(function ($query) {
                    $query->where('korpus_id', $this->korpus_id)
                        ->where('number', $this->number);
                })],
        ];
    }
}
