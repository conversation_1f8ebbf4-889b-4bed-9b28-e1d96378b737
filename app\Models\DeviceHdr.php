<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\DeviceHdr
 *
 * @mixin IdeHelperDeviceHdr
 *
 * @property int $id
 * @property int $sukh_id
 * @property int $device_config_id
 * @property string|null $description
 * @property bool $is_disabled
 * @property bool $skip_fcnt_check
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Sukh|null $device_config
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DeviceDtl> $device_dtls
 * @property-read int|null $device_dtls_count
 * @property-read \App\Models\Sukh|null $sukh
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereDeviceConfigId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereIsDisabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereSkipFcntCheck($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereSukhId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceHdr whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DeviceHdr extends Model
{
    use HasFactory;

    const ID = 'id';

    const SUKH_ID = 'sukh_id';

    const DEVICE_CONFIG_ID = 'device_config_id';

    const DESCRIPTION = 'description';

    const IS_DISABLED = 'is_disabled';

    const SKIP_FCNT_CHECK = 'skip_fcnt_check';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::SUKH_ID,
        self::DEVICE_CONFIG_ID,
        self::DESCRIPTION,
        self::IS_DISABLED,
        self::SKIP_FCNT_CHECK,
    ];

    public function sukh(): BelongsTo
    {
        return $this->belongsTo(Sukh::class);
    }

    public function device_config(): BelongsTo
    {
        return $this->belongsTo(Sukh::class);
    }

    public function device_dtls(): HasMany
    {
        return $this->hasMany(DeviceDtl::class);
    }
}
