<?php

use App\Enums\NehemjlehStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nehemjlehs', function (Blueprint $table) {
            $table->id();
            $table->smallInteger('year');
            $table->tinyInteger('month');
            $table->unsignedBigInteger('bair_id');
            $table->string('bair_name');
            $table->unsignedBigInteger('korpus_id'); // Changed from orc_id
            $table->string('korpus_name'); // Changed from orc_number
            $table->string('toot_number');
            $table->string('orshin_suugch_ovog')->nullable(); // Made nullable
            $table->string('orshin_suugch_ner');
            $table->string('utas');
            $table->unsignedDecimal('niit_dun', 24, 2);
            $table->unsignedDecimal('tulsun_dun', 24, 2);
            $table->unsignedDecimal('uldegdel_dun', 24, 2);
            $table->enum('status', NehemjlehStatusEnum::getValues())->default(NehemjlehStatusEnum::TULUGDUUGUI);
            $table->unsignedBigInteger('code')->length(16)->unique();
            $table->unsignedBigInteger('orshin_suugch_id')->nullable();
            $table->unsignedBigInteger('sukh_id')->nullable();
            $table->string('sukh_name')->nullable();
            $table->string('orshin_suugch_uniq_code')->nullable();
            $table->unsignedDecimal('niit_nuatgui_dun', 24, 2);
            $table->unsignedDecimal('nuat_dun', 24, 2);
            $table->unsignedBigInteger('aimag_id')->nullable();
            $table->string('aimag_name')->nullable();
            $table->unsignedBigInteger('soum_id')->nullable();
            $table->string('soum_name')->nullable();
            $table->unsignedBigInteger('bag_id')->nullable();
            $table->string('bag_name')->nullable();
            $table->string('sukh_registration_number')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nehemjlehs');
    }
};
