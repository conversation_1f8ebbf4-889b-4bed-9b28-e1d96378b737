<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\TableName
 *
 * @mixin IdeHelperTableName
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ColumnName> $column_names
 * @property-read int|null $column_names_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TableName newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TableName newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TableName query()
 * @method static \Illuminate\Database\Eloquent\Builder|TableName whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TableName whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TableName whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TableName whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class TableName extends Model
{
    use HasFactory;

    const ID = 'id';

    const NAME = 'name';

    const RELATION_COLUMN_NAMES = 'column_names';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
    ];

    public function getOrignalTableName()
    {
        if (! str_contains($this->name, '.')) {
            return $this->name;
        }

        $tableNames = explode('.', $this->name);

        return $tableNames[count($tableNames) - 1];
    }

    public function getSubTableNames()
    {
        if (! str_contains($this->name, '.')) {
            return [$this->name];
        }

        return $tableNames = explode('.', $this->name);
    }

    public function column_names(): HasMany
    {
        return $this->hasMany(ColumnName::class);
    }
}
