<?php

namespace App\Filament\Resources\SuperAdmin\DeviceHdrResource\Pages;

use App\Filament\Resources\SuperAdmin\DeviceHdrResource;
use App\Models\DeviceDtl;
use App\Models\DeviceHdr;
use App\Services\ChirpStackService;
use App\Services\SuperAdminService;
use App\Services\ToolService;
use Filament\Resources\Pages\CreateRecord;

class CreateDeviceHdr extends CreateRecord
{
    protected static string $resource = DeviceHdrResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[DeviceHdr::IS_DISABLED] = false;
        $data[DeviceHdr::SKIP_FCNT_CHECK] = true;

        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        $toolService = resolve(ToolService::class);
        $service = resolve(SuperAdminService::class);
        $sukh = $service->getSukhWithOrcs($record->sukh_id);
        $newDevices = [];
        foreach ($sukh->bairs as $key => $bair) {
            if (empty($bair->korpuses)) {
                continue;
            }
            foreach ($bair->korpuses as $key => $korpuse) {
                foreach ($korpuse->orcs as $key => $orc) {
                    $newDevice = new DeviceDtl([
                        DeviceDtl::DEVICE_HDR_ID => $record->id,
                        DeviceDtl::KORPUS_ID => $orc->korpus_id,
                        DeviceDtl::ORC_ID => $orc->id,
                        DeviceDtl::NAME => $sukh->name.'_'.$bair->name.'_'.$korpuse->name.'_'.$orc->number,
                        DeviceDtl::DEV_EUI => $toolService->generateUniqId(),
                        DeviceDtl::JOIN_EUI => $toolService->generateUniqId(),
                        DeviceDtl::NWK_KEY => $toolService->generateUniqId(32),
                    ]);
                    $newDevices[] = $newDevice;
                }
            }
        }
        $record->device_dtls()->saveMany($newDevices);

        $service = resolve(ChirpStackService::class);
        foreach ($newDevices as $key => $newDevice) {
            $service->postDevice($record, $newDevice);
        }
    }
}
