<?php

namespace App\Filament\Resources\Admin\NehemjlehTohirgooResource\RelationManagers;

use App\Models\NehemjlehTohirgooDtl;
use App\Services\UserInfoService;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class NehemjlehTohirgooDtlsRelationManager extends RelationManager
{
    protected static string $relationship = 'nehemjleh_tohirgoo_dtls';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Тоотууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('number')
                    ->label('Тоот')
                    ->searchable('toots.number'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('Нэмэх')
                    ->form([
                        Select::make('bair_id')
                            ->label('Байр')
                            ->searchable()
                            ->live()
                            ->options(
                                function () {
                                    $service = resolve(UserInfoService::class);

                                    return $service->getAUBairs();
                                }
                            ),
                        Select::make('korpus_id')
                            ->label('Блок')
                            ->live()
                            ->options(
                                function (callable $get) {
                                    $service = resolve(UserInfoService::class);

                                    return $service->getAUKorpuses($get('bair_id'));
                                }
                            ),
                        Select::make('toot_numbers')
                            ->label('Тоотууд')
                            ->multiple()
                            ->options(
                                function (callable $get) {
                                    $service = resolve(UserInfoService::class);

                                    return $service->getAUTootsKeyNumberWithDoesntHave($get('bair_id'), $get('korpus_id'));
                                }
                            ),
                    ])
                    ->action(function (array $data) {
                        foreach ($data['toot_numbers'] as $key => $number) {
                            NehemjlehTohirgooDtl::create([
                                NehemjlehTohirgooDtl::NEHEMJLEH_TOHIRGOO_ID => $this->ownerRecord->id,
                                NehemjlehTohirgooDtl::BAIR_ID => $data['bair_id'],
                                NehemjlehTohirgooDtl::KORPUS_ID => $data['korpus_id'],
                                NehemjlehTohirgooDtl::NUMBER => $number,
                            ]);
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('Устгах')
                    ->color('danger')
                    ->action(function ($record) {
                        // NehemjlehTohirgooToot::where(NehemjlehTohirgooToot::NEHEMJLEH_TOHIRGOO_ID, $this->ownerRecord->id)
                        //     ->where(NehemjlehTohirgooToot::NUMBER, $record->number)
                        //     ->delete();
                    }),
            ])
            ->bulkActions([
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }
}
