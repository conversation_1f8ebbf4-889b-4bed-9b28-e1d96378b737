<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Filament\Resources\Admin\BairResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBairs extends ListRecords
{
    protected static string $resource = BairResource::class;

    protected static ?string $title = 'Байрны жагсаалт';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
