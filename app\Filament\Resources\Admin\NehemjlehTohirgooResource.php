<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\NehemjlehTohirgooResource\Pages;
use App\Filament\Resources\Admin\NehemjlehTohirgooResource\RelationManagers;
use App\Models\NehemjlehTohirgoo;
use App\Models\Uilchilgee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class NehemjlehTohirgooResource extends Resource
{
    protected static ?string $model = NehemjlehTohirgoo::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Нэхэмжлэхийн тохиргоо';

    protected static ?string $modelLabel = 'Нэхэмжлэхийн тохиргоо';

    protected static ?int $navigationSort = 4;

    protected static ?string $slug = 'nehemjleh_tohirgoo';

    protected static ?string $navigationGroup = 'Биллинг';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(NehemjlehTohirgoo::NAME)
                            ->label('Нэр')
                            ->required(),
                        Forms\Components\Select::make(NehemjlehTohirgoo::RELATION_UILCHILGEES)
                            ->label('Үйлчилгээ')
                            ->options(Uilchilgee::doesntHave(Uilchilgee::RELATION_NEHEMJLEH_TOHIRGOO)->get()->pluck(NehemjlehTohirgoo::NAME, NehemjlehTohirgoo::ID))
                            ->relationship(NehemjlehTohirgoo::RELATION_UILCHILGEES, Uilchilgee::NAME)
                            ->multiple()
                            ->required()
                            ->searchable(),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?NehemjlehTohirgoo $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (NehemjlehTohirgoo $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (NehemjlehTohirgoo $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?NehemjlehTohirgoo $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(NehemjlehTohirgoo::NAME)->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\NehemjlehTohirgooDtlsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNehemjlehTohirgoos::route('/'),
            'create' => Pages\CreateNehemjlehTohirgoo::route('/create'),
            'edit' => Pages\EditNehemjlehTohirgoo::route('/{record}/edit'),
        ];
    }
}
