<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateMemberRequest extends FormRequest
{
    const PARAMETER_LAST_NAME = 'last_name';

    const PARAMETER_FIRST_NAME = 'first_name';

    const PARAMETER_PHONE = 'phone';

    const PARAMETER_ORSHIN_SUUGCH_TOOT_IDS = 'orshin_suugch_toot_ids';

    const PARAMETER_ORSHIN_SUUGCH_TOOT_ID = 'orshin_suugch_toot_ids.*';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_LAST_NAME => 'nullable|string',
            self::PARAMETER_FIRST_NAME => 'nullable|string',
            self::PARAMETER_PHONE => 'required|unique:orshin_suugches,phone|size:8',
            self::PARAMETER_ORSHIN_SUUGCH_TOOT_IDS => 'nullable|array',
            self::PARAMETER_ORSHIN_SUUGCH_TOOT_ID => 'numeric|exists:orshin_suugch_toots,id',
        ];
    }
}
