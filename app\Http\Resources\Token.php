<?php

namespace App\Http\Resources;

use App\Models\Constant\ConstData;
use Illuminate\Http\Resources\Json\JsonResource;

class Token extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'status' => ConstData::STATUS_SUCCESS,
            'message' => ConstData::TOKEN,
            'value' => $this->plainTextToken,
        ];
    }
}
