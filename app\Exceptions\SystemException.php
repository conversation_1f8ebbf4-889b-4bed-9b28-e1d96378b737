<?php

namespace App\Exceptions;

use App\Models\Constant\ConstData;
use App\Models\Constant\Message;
use Exception;

class SystemException extends Exception
{
    private $exceptionType;

    private $errorCode;

    private $errors;

    private $additionalMsg;

    public function __construct($exceptionType, $errorCode, $errors = [], $additionalMsg = null)
    {
        $this->exceptionType = $exceptionType;
        $this->errorCode = $errorCode;
        $this->errors = $errors;
        $this->additionalMsg = $additionalMsg;
    }

    /**
     * Report the exception.
     *
     * @return bool|null
     */
    public function report()
    {
        //
    }

    public function prepareMessage()
    {
        $message = '';
        $errorCode = $this->errorCode;
        switch ($this->exceptionType) {
            case ConstData::AUTH_EXCEPTION:
                $message = Message::getAuthMessage($errorCode);
                break;
            case ConstData::SYSTEM_EXCEPTION:
                $message = Message::getSystemMessage($errorCode);
                break;
            case ConstData::QPAY_EXCEPTION:
                $message = Message::getQpayMessage($errorCode);
                break;
            case ConstData::BPAY_EXCEPTION:
                $message = Message::getBpayMessage($errorCode);
                break;
            case ConstData::ORSHIN_SUUGCH_EXCEPTION:
                $message = Message::getOrshinSuugchMessage($errorCode);
                break;
            case ConstData::ERKH_EXCEPTION:
                $message = Message::getErkhMessage($errorCode);
                break;
            default:
                break;
        }

        return $message;
    }

    /**
     * Render the exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function render($request)
    {
        $message = $this->prepareMessage();
        $additionalMsg = $this->additionalMsg ? $this->additionalMsg : '';

        return response()->json([ConstData::STATUS => ConstData::STATUS_ERROR, ConstData::MESSAGE => ($this->exceptionType.$message.' '.$additionalMsg), 'errors' => $this->errors], 501);
    }
}
