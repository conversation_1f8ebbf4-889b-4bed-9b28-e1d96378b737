<?php

namespace App\Console\Commands;

use App\Models\Bair;
use App\Models\Korpus;
use App\Services\BairSyncService;
use App\Services\CvSecurityService\CvSecurityService;
use Illuminate\Console\Command;

class TestBairCvSecurityIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:bair-cv-security {action=status : Action to perform (status|update|sync-all)}';

    /**
     * The console description of the command.
     *
     * @var string
     */
    protected $description = 'Test Bair CVSecurity integration (updates related Korpus buildings)';

    protected CvSecurityService $cvSecurityService;

    protected BairSyncService $bairSyncService;

    public function __construct(CvSecurityService $cvSecurityService, BairSyncService $bairSyncService)
    {
        parent::__construct();
        $this->cvSecurityService = $cvSecurityService;
        $this->bairSyncService = $bairSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->checkServiceStatus();
                break;
            case 'update':
                $this->testBairUpdate();
                break;
            case 'sync-all':
                $this->testSyncAllKorpusBuildings();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: status, update, sync-all');

                return 1;
        }

        return 0;
    }

    protected function checkServiceStatus()
    {
        $this->info('Checking CVSecurity service status...');

        $isAvailable = $this->cvSecurityService->isServiceAvailable();

        if ($isAvailable) {
            $this->info('✅ CVSecurity service is available');
        } else {
            $this->warn('⚠️ CVSecurity service is not available');
            $this->info('Please check:');
            $this->info('- Service is running');
            $this->info('- Environment variables are set correctly');
            $this->info('- Network connectivity');
        }

        // Show configuration
        $this->info("\nConfiguration:");
        $this->table(
            ['Setting', 'Value'],
            [
                ['Host', config('services.cv_security_ext.host', 'Not set')],
                ['Port', config('services.cv_security_ext.port', 'Not set')],
                ['API Key', config('services.cv_security_ext.api_key') ? 'Set' : 'Not set'],
            ]
        );

        // Show Bair and Korpus statistics
        $this->info("\nDatabase Statistics:");
        $bairCount = Bair::count();
        $korpusCount = Korpus::count();
        $korpusWithCodeCount = Korpus::whereNotNull('code')->count();

        $this->table(
            ['Entity', 'Count'],
            [
                ['Total Bairs', $bairCount],
                ['Total Korpus', $korpusCount],
                ['Korpus with CVSecurity codes', $korpusWithCodeCount],
            ]
        );
    }

    protected function testBairUpdate()
    {
        $this->info('Testing Bair name update and related Korpus building sync...');

        // Find a Bair with Korpus that have CVSecurity codes
        $bair = Bair::whereHas('korpuses', function ($query) {
            $query->whereNotNull('code');
        })->first();

        if (! $bair) {
            $this->error('No Bair found with Korpus that have CVSecurity codes.');
            $this->info('Please create some Korpus first using: php artisan test:korpus-cv-security create');

            return;
        }

        $this->info("Found Bair: {$bair->name} (ID: {$bair->id})");

        // Get related Korpus with codes
        $korpuses = $bair->korpuses()->whereNotNull('code')->get();
        $this->info("Related Korpus with CVSecurity codes: {$korpuses->count()}");

        if ($korpuses->isEmpty()) {
            $this->warn('No Korpus with CVSecurity codes found for this Bair');

            return;
        }

        // Show current building names
        $this->info("\nCurrent building names in CVSecurity:");
        foreach ($korpuses as $korpus) {
            $separator = is_numeric(substr($korpus->name, 0, 1)) ? '-' : '';
            $currentBuildingName = $bair->name.$separator.$korpus->name;
            $this->line("- Korpus '{$korpus->name}' → '{$currentBuildingName}' (Code: {$korpus->code})");
        }

        // Update Bair name
        $originalName = $bair->name;
        $newName = $originalName.' (Updated '.now()->format('H:i:s').')';

        $this->info("\nUpdating Bair name from '{$originalName}' to '{$newName}'...");
        $bair->update(['name' => $newName]);

        // Wait a moment for the observer to process
        sleep(2);

        // Show new building names
        $this->info("\nNew building names in CVSecurity:");
        foreach ($korpuses as $korpus) {
            $korpus->refresh(); // Refresh to get any updated data
            $separator = is_numeric(substr($korpus->name, 0, 1)) ? '-' : '';
            $newBuildingName = $newName.$separator.$korpus->name;
            $this->line("- Korpus '{$korpus->name}' → '{$newBuildingName}' (Code: {$korpus->code})");
        }

        $this->info("\n✅ Bair name update completed");
        $this->info('All related Korpus buildings should have been updated in CVSecurity automatically');

        // Revert the name change for testing purposes
        if ($this->confirm('Do you want to revert the Bair name back to original?', true)) {
            $this->info("Reverting Bair name back to '{$originalName}'...");
            $bair->update(['name' => $originalName]);
            sleep(2);
            $this->info('✅ Bair name reverted');
        }
    }

    protected function testSyncAllKorpusBuildings()
    {
        $this->info('Testing manual sync of all Korpus buildings for a Bair...');

        // Find a Bair with Korpus
        $bair = Bair::has('korpuses')->first();

        if (! $bair) {
            $this->error('No Bair found with Korpus.');
            $this->info('Please create some Korpus first using: php artisan test:korpus-cv-security create');

            return;
        }

        $this->info("Found Bair: {$bair->name} (ID: {$bair->id})");

        $korpusCount = $bair->korpuses()->count();
        $this->info("Total Korpus in this Bair: {$korpusCount}");

        if ($korpusCount === 0) {
            $this->warn('No Korpus found for this Bair');

            return;
        }

        // Perform manual sync
        $this->info("\nPerforming manual sync of all Korpus buildings...");
        $results = $this->bairSyncService->syncAllKorpusBuildings($bair);

        // Display results
        $this->info("\n✅ Manual sync completed");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Korpus', $results['total']],
                ['Successful syncs', $results['successful']],
                ['Failed syncs', $results['failed']],
                ['Skipped', $results['skipped']],
            ]
        );

        if (! empty($results['details'])) {
            $this->info("\nDetailed results:");
            $detailsTable = [];
            foreach ($results['details'] as $detail) {
                $detailsTable[] = [
                    'Korpus ID' => $detail['korpus_id'],
                    'Action' => $detail['action'],
                    'Status' => $detail['status'],
                    'Error' => $detail['error'] ?? '-',
                ];
            }
            $this->table(['Korpus ID', 'Action', 'Status', 'Error'], $detailsTable);
        }
    }
}
