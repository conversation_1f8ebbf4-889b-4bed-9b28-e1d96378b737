<?php

namespace App\Services;

use App\Models\Orc;

class OrcService
{
    public function getOrc($korpusId, $number)
    {
        // Note: Door numbering logic has been moved to hierarchical UI-based configuration
        // The $number parameter is kept for backward compatibility but not currently used
        // TODO: Implement proper hierarchical door numbering logic
        $orc = Orc::where(Orc::KORPUS_ID, $korpusId)->first();

        return $orc;
    }
}
