<?php

namespace App\Services;

use App\Models\OrshinSuugchToot;
use App\Services\CvSecurityService\CvSecurityService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

/**
 * OrshinSuugchToot Synchronization Service
 *
 * This service handles the synchronization of OrshinSuugchToot (person assignment) data
 * with the CVSecurity system.
 */
class OrshinSuugchTootSyncService
{
    protected CvSecurityService $cvSecurityService;

    protected CvSecurityServiceExt $cvSecurityServiceExt;

    public function __construct(CvSecurityService $cvSecurityService, CvSecurityServiceExt $cvSecurityServiceExt)
    {
        $this->cvSecurityService = $cvSecurityService;
        $this->cvSecurityServiceExt = $cvSecurityServiceExt;
    }

    /**
     * Synchronize OrshinSuugchToot creation with CVSecurity
     */
    public function syncCreate(OrshinSuugchToot $orshinSuugchToot): void
    {
        try {
            $orshinSuugch = $orshinSuugchToot->orshin_suugch;

            Log::info('OrshinSuugchTootSyncService: Starting create sync', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'orshin_suugch_id' => $orshinSuugch->id,
                'name' => $orshinSuugch->name,
                'phone' => $orshinSuugch->phone,
            ]);

            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchTootSyncService: CVSecurity service is not available for create sync', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // Check if CVSecurity sync is enabled
            if (config('services.cv_security.sync_enabled', true) === false) {
                Log::info('OrshinSuugchTootSyncService: CVSecurity sync is disabled', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // Get next available pin from CVSecurity EXT service if no pin exists
            $pin = $orshinSuugchToot->pin ?? $this->cvSecurityServiceExt->getNextPersonPin();
            if (! $pin) {
                Log::error('OrshinSuugchTootSyncService: Failed to get next person pin', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // Prepare person data for CVSecurity
            $personData = $this->mapToPersonData($orshinSuugchToot, $pin);

            // Create person in CVSecurity
            $response = $this->cvSecurityService->createOrUpdatePerson($personData);

            if ($response) {
                // Extract pin from response and store it in the pin field
                $code = $this->extractCodeFromResponse($response);

                if ($code) {
                    // If the response indicates success but doesn't return the pin,
                    // use the original pin that was sent to CVSecurity
                    $finalCode = ($code === 'success') ? $pin : $code;

                    $orshinSuugchToot->update(['pin' => $finalCode]);

                    Log::info('OrshinSuugchTootSyncService: Create sync successful', [
                        'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                        'cv_code' => $finalCode,
                        'original_pin' => $pin,
                    ]);
                } else {
                    Log::warning('OrshinSuugchTootSyncService: Could not extract code from response', [
                        'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                        'response' => $response,
                    ]);
                }
            } else {
                Log::error('OrshinSuugchTootSyncService: Create sync failed', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchTootSyncService: Create sync exception', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Synchronize OrshinSuugchToot update with CVSecurity
     */
    public function syncUpdate(OrshinSuugchToot $orshinSuugchToot): void
    {
        try {
            $orshinSuugch = $orshinSuugchToot->orshin_suugch;

            Log::info('OrshinSuugchTootSyncService: Starting update sync', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'cv_code' => $orshinSuugchToot->pin,
            ]);

            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchTootSyncService: CVSecurity service is not available for update sync', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // If no pin exists, treat as create
            if (! $orshinSuugchToot->pin) {
                Log::info('OrshinSuugchTootSyncService: No pin found, treating update as create', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);
                $this->syncCreate($orshinSuugchToot);

                return;
            }

            // Prepare person data for CVSecurity
            $personData = $this->mapToPersonData($orshinSuugchToot, $orshinSuugchToot->pin);

            // Update person in CVSecurity
            $response = $this->cvSecurityService->createOrUpdatePerson($personData);

            if ($response) {
                Log::info('OrshinSuugchTootSyncService: Update sync successful', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                    'cv_code' => $orshinSuugchToot->pin,
                ]);
            } else {
                Log::error('OrshinSuugchTootSyncService: Update sync failed', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                    'cv_code' => $orshinSuugchToot->pin,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchTootSyncService: Update sync exception', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Synchronize OrshinSuugchToot deletion with CVSecurity
     */
    public function syncDelete(OrshinSuugchToot $orshinSuugchToot): void
    {
        try {
            $orshinSuugch = $orshinSuugchToot->orshin_suugch;

            Log::info('OrshinSuugchTootSyncService: Starting delete sync', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'cv_code' => $orshinSuugchToot->pin,
            ]);

            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchTootSyncService: CVSecurity service is not available for delete sync', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // If no pin exists, nothing to delete
            if (! $orshinSuugchToot->pin) {
                Log::info('OrshinSuugchTootSyncService: No pin found, skipping delete sync', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                ]);

                return;
            }

            // Delete person from CVSecurity
            $response = $this->cvSecurityService->deletePerson($orshinSuugchToot->pin);

            if ($response) {
                Log::info('OrshinSuugchTootSyncService: Delete sync successful', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                    'cv_code' => $orshinSuugchToot->pin,
                ]);
            } else {
                Log::error('OrshinSuugchTootSyncService: Delete sync failed', [
                    'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                    'cv_code' => $orshinSuugchToot->pin,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchTootSyncService: Delete sync exception', [
                'orshin_suugch_toot_id' => $orshinSuugchToot->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Map OrshinSuugchToot model to CVSecurity person data format
     */
    protected function mapToPersonData(OrshinSuugchToot $orshinSuugchToot, string $pin): array
    {
        $orshinSuugch = $orshinSuugchToot->orshin_suugch;
        $deptCode = $orshinSuugchToot->toot?->orc?->code ?? null;
        $tootIdPrefix = $orshinSuugchToot->id.'_';

        Log::info('OrshinSuugchTootSyncService: Mapping person data', [
            'orshin_suugch_toot_id' => $orshinSuugchToot->id,
            'orshin_suugch_id' => $orshinSuugch->id,
            'dept_code' => $deptCode,
            'toot_id_prefix' => $tootIdPrefix,
        ]);

        return [
            'pin' => $pin,
            'name' => $orshinSuugch->name ?? '',
            'lastName' => $orshinSuugch->last_name ?? '',
            'mobilePhone' => $tootIdPrefix.($orshinSuugch->phone ?? ''),
            'email' => $orshinSuugch->email ? ($tootIdPrefix.$orshinSuugch->email) : null,
            'deptCode' => $deptCode,
        ];
    }

    /**
     * Extract code from CVSecurity response
     */
    protected function extractCodeFromResponse(object $response): ?string
    {
        // Check if response indicates success
        if (isset($response->code) && $response->code === 0 && isset($response->message) && $response->message === 'success') {
            // For CVSecurity API, if the operation is successful but data is null,
            // we should use the original pin that was sent in the request
            // This will be handled by the calling method
            return 'success';
        }

        // Based on the API documentation, the response should contain the pin
        if (isset($response->data) && isset($response->data->pin)) {
            return $response->data->pin;
        }

        if (isset($response->pin)) {
            return $response->pin;
        }

        // If response format is different, log it for debugging
        Log::debug('OrshinSuugchTootSyncService: Unexpected response format', [
            'response' => $response,
        ]);

        return null;
    }
}
