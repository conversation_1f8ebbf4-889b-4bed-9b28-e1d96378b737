<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Package extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'is_free' => $this->is_free,
            'is_limitless' => $this->is_limitless,
            'price' => $this->price,
            'duration_unit' => $this->duration_unit,
            'duration_value' => $this->duration_value,
            'products' => $this->products,
        ];
    }
}
