<?php

namespace App\Filament\Resources\SuperAdmin\ContractResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractResource;
use App\Services\StorageService;
use Filament\Resources\Pages\CreateRecord;

class CreateContract extends CreateRecord
{
    protected static string $resource = ContractResource::class;

    protected function afterCreate(): void
    {
        $service = resolve(StorageService::class);
        $service->createContractPDF($this->record);
    }
}
