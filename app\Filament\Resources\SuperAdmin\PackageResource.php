<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Enums\ProductEnum;
use App\Filament\Resources\SuperAdmin\PackageResource\Pages;
use App\Models\Package;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PackageResource extends Resource
{
    protected static ?string $model = Package::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Багц';

    protected static ?string $modelLabel = 'багц';

    protected static ?int $navigationSort = 3;

    protected static ?string $slug = 'packages';

    protected static ?string $navigationGroup = 'Тохиргоо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('products')
                            ->label('Бүтээгдэхүүн')
                            ->default(0)
                            ->options(ProductEnum::getProductsWithName())
                            ->multiple()
                            ->required(),

                        Forms\Components\TextInput::make('name')
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),

                        Forms\Components\Toggle::make('is_free')
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-user')
                            ->label('Төлбөргүй эсэх')
                            ->live(),

                        Forms\Components\TextInput::make('price')
                            ->label('Суурь үнэ')
                            ->default(0)
                            ->numeric()
                            ->maxValue(100000000)
                            ->hidden(function (callable $get) {
                                return $get('is_free');
                            }),

                        Forms\Components\TextInput::make('valid_day')
                            ->label('Хүчинтэй өдөр')
                            ->default(0)
                            ->numeric()
                            ->minValue(7)
                            ->maxValue(100)
                            ->hidden(function (callable $get) {
                                return ! $get('is_free');
                            }),

                        Forms\Components\Toggle::make('is_new_os_erkh')
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-user')
                            ->label('Шинэ хэрэглэгчийн эрх эсэх')
                            ->hidden(function (callable $get) {
                                return ! $get('is_free');
                            })
                            ->live(),

                        Forms\Components\Toggle::make('is_limitless')
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-user')
                            ->label('Хязгааргүй эсэх')
                            ->hidden(function (callable $get) {
                                return ! $get('is_free');
                            }),

                        Forms\Components\Select::make('sukhs')
                            ->label('СӨХ')
                            ->multiple()
                            ->relationship(name: 'sukhs', titleAttribute: 'name'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?Package $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Package $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Package $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Package $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('productNames')->label('Бүтээгдэхүүн')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_free')->label('Үнэгүй эсэх')->sortable(),
                Tables\Columns\TextColumn::make('valid_day')->label('Хүчинтэй өдөр')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_limitless')->label('Хязгааргүй эсэх')->sortable(),
                Tables\Columns\BooleanColumn::make('is_new_os_erkh')->label('Шинэ хэрэглэгчийнх эсэх')->sortable(),
                Tables\Columns\TextColumn::make('duration_unit')->formatStateUsing(fn (string $state): string => $state == 0 ? 'Хоног' : ($state == 1 ? 'Сар' : ($state == 2 ? 'Жил' : 'Хоосон')))->label('Хугацааны нэгж'),
                Tables\Columns\TextColumn::make('duration_value')->label('хугацааны утга')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPackages::route('/'),
            'create' => Pages\CreatePackage::route('/create'),
            'view' => Pages\ViewPackage::route('/{record}'),
        ];
    }
}
