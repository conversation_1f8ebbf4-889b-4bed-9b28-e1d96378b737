<?php

namespace App\Services;

use Carbon\Carbon;
use Exception;

class ToolService
{
    public function __construct() {}

    public function generateUniqId($lenght = 16)
    {
        if (function_exists('random_bytes')) {
            $bytes = random_bytes(ceil($lenght / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            $bytes = openssl_random_pseudo_bytes(ceil($lenght / 2));
        } else {
            throw new Exception('no cryptographically secure random function available');
        }

        return substr(bin2hex($bytes), 0, $lenght);
    }

    public function random_digits($length)
    {
        $result = '';
        for ($i = 0; $i < $length; $i++) {
            $result .= random_int(0, 9);
        }

        return $result;
    }

    public function generateCodeFromDatetime()
    {
        $datetime = Carbon::now();
        $milliseconds = $datetime->format('v'); // 3 digits
        $microseconds = $datetime->microsecond; // 6 digits
        $extraDigit = substr($microseconds, -1); // Get the last digit of microseconds

        return $datetime->format('ymdHis').$milliseconds.$extraDigit; // Total 16 characters
    }

    public function generateCode($length = 12)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-+=<>?';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }
}
