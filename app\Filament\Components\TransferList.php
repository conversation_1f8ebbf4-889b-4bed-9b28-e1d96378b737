<?php

namespace App\Filament\Components;

use Closure;
use Filament\Forms\Components\Field;

class TransferList extends Field
{
    protected string $view = 'filament.components.transfer-list';

    protected array|Closure $availableItems = [];

    protected array|Closure $selectedItems = [];

    protected string $leftTitle = 'Available';

    protected string $rightTitle = 'Selected';

    protected string $keyField = 'id';

    protected string $labelField = 'name';

    protected int $height = 200;

    public function availableItems(array|Closure $items): static
    {
        $this->availableItems = $items;

        return $this;
    }

    public function getAvailableItems(): array
    {
        return $this->evaluate($this->availableItems);
    }

    public function selectedItems(array|Closure $items): static
    {
        $this->selectedItems = $items;

        return $this;
    }

    public function getSelectedItems(): array
    {
        return $this->evaluate($this->selectedItems);
    }

    public function leftTitle(string $title): static
    {
        $this->leftTitle = $title;

        return $this;
    }

    public function getLeftTitle(): string
    {
        return $this->leftTitle;
    }

    public function rightTitle(string $title): static
    {
        $this->rightTitle = $title;

        return $this;
    }

    public function getRightTitle(): string
    {
        return $this->rightTitle;
    }

    public function keyField(string $field): static
    {
        $this->keyField = $field;

        return $this;
    }

    public function getKeyField(): string
    {
        return $this->keyField;
    }

    public function labelField(string $field): static
    {
        $this->labelField = $field;

        return $this;
    }

    public function getLabelField(): string
    {
        return $this->labelField;
    }

    public function height(int $height): static
    {
        $this->height = $height;

        return $this;
    }

    public function getHeight(): int
    {
        return $this->height;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->default([
            'available' => [],
            'selected' => [],
        ]);

        $this->live();
    }
}
