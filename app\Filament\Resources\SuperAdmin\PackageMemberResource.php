<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\PackageMemberResource\Pages;
use App\Models\Constant\ConstData;
use App\Models\Package;
use App\Models\PackageMember;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;

class PackageMemberResource extends Resource
{
    protected static ?string $model = PackageMember::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Багц гишүүд';

    protected static ?string $modelLabel = 'багц гишүүды';

    protected static ?int $navigationSort = 100;

    protected static ?string $slug = 'packageMembers';

    protected static ?string $navigationGroup = 'Үнэ тохиргоо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(PackageMember::PACKAGE_ID)
                            ->label('Багц')
                            ->options(Package::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->required(),

                        Forms\Components\TextInput::make(PackageMember::VALUE)
                            ->label('Гишүүдийн тоо')
                            ->numeric()
                            ->inputMode('decimal')
                            ->maxValue(240),

                        Forms\Components\TextInput::make(PackageMember::DISCOUNT)
                            ->label('Хямдралын хувь')
                            ->default(0)
                            ->numeric()
                            ->maxValue(99.9)
                            ->suffix('%'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?PackageMember $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (PackageMember $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (PackageMember $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?PackageMember $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(PackageMember::RELATION_PACKAGE.'.'.Package::NAME)->label('Багц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(PackageMember::VALUE)->label('Гишүүдийн тоо')
                    ->icon('heroicon-m-calendar')
                    ->iconPosition(IconPosition::After)->sortable()->searchable(),
                Tables\Columns\TextColumn::make(PackageMember::DISCOUNT)->label('Хямдралын хувь')
                    ->icon('heroicon-m-receipt-percent')
                    ->iconPosition(IconPosition::After)->sortable()->searchable(),
            ])
            ->defaultSort(PackageMember::VALUE, 'asc')
            ->filters([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([

            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPackageMembers::route('/'),
            'create' => Pages\CreatePackageMember::route('/create'),
            'edit' => Pages\EditPackageMember::route('/{record}/edit'),
        ];
    }
}
