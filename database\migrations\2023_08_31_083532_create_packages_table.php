<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->boolean('is_free')->default(false);
            $table->boolean('is_limitless')->default(false);
            $table->boolean('is_new_os_erkh')->default(false);
            $table->decimal('price', 24, 2)->default(0);
            $table->string('products')->nullable(); // Replaced duration_unit, duration_value, product enum
            $table->integer('valid_day')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
