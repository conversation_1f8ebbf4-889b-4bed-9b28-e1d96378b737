<?php

namespace App\Console\Commands;

use App\Models\LiftGroup;
use App\Models\Toot;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use App\Services\LiftGroupDeviceDavharSyncService;
use Illuminate\Console\Command;

class TestLiftGroupDavharCvSecurityIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:lift-group-davhar-cv-security {action=status : The action to perform (status|create|update|delete)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test LiftGroupDavhar CVSecurity integration';

    protected CvSecurityServiceExt $cvSecurityService;

    protected LiftGroupDeviceDavharSyncService $liftGroupDeviceDavharSyncService;

    public function __construct(CvSecurityServiceExt $cvSecurityService, LiftGroupDeviceDavharSyncService $liftGroupDeviceDavharSyncService)
    {
        parent::__construct();
        $this->cvSecurityService = $cvSecurityService;
        $this->liftGroupDeviceDavharSyncService = $liftGroupDeviceDavharSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->testServiceStatus();
                break;
            case 'create':
                $this->testCreateLiftGroupDavhar();
                break;
            case 'update':
                $this->testUpdateLiftGroupDavhar();
                break;
            case 'delete':
                $this->testDeleteLiftGroupDavhar();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: status, create, update, delete');
        }
    }

    protected function testServiceStatus()
    {
        $this->info('Testing CVSecurity service status...');

        $status = $this->cvSecurityService->getServiceStatus();

        $this->table(
            ['Property', 'Value'],
            [
                ['Service', $status['service']],
                ['Status', $status['status']],
                ['Host', $status['host']],
                ['Port', $status['port']],
            ]
        );

        if ($status['status'] === 'online') {
            $this->info('✅ CVSecurity service is available');
        } else {
            $this->error('❌ CVSecurity service is not available');
        }
    }

    protected function testCreateLiftGroupDavhar()
    {
        $this->info('Testing LiftGroupDavhar creation with CVSecurity integration...');

        // Find a lift group and davhar for testing
        $liftGroup = LiftGroup::first();
        $davhar = Davhar::whereHas('orc', function ($query) {
            $query->whereNotNull('code');
        })
            ->with(['orc'])
            ->first();

        if (! $liftGroup) {
            $this->error('❌ No lift group found for testing');

            return;
        }

        if (! $davhar) {
            $this->error('❌ No davhar with valid orc code found for testing');

            return;
        }

        $this->info('Creating test LiftGroupDavhar:');
        $this->info("- Lift Group: {$liftGroup->name} (ID: {$liftGroup->id})");
        $this->info("- Davhar: {$davhar->number} (ID: {$davhar->id})");
        $this->info("- Orc Code: {$davhar->orc->code}");

        try {
            $liftGroupDavhar = LiftGroupDavhar::create([
                'lift_group_device_id' => $liftGroup->id,
                'davhar_id' => $davhar->id,
            ]);

            $this->info("✅ LiftGroupDavhar created with ID: {$liftGroupDavhar->id}");

            // Wait a moment for observer to process
            sleep(1);

            // Refresh to get the code
            $liftGroupDavhar->refresh();

            if ($liftGroupDavhar->code) {
                $this->info("✅ CVSecurity code assigned: {$liftGroupDavhar->code}");
            } else {
                $this->warn('⚠️ No CVSecurity code was assigned');
            }

        } catch (\Exception $e) {
            $this->error("❌ Failed to create LiftGroupDavhar: {$e->getMessage()}");
        }
    }

    protected function testUpdateLiftGroupDavhar()
    {
        $this->info('Testing LiftGroupDavhar update with CVSecurity integration...');

        // Find a lift group toot with a code
        $liftGroupDavhar = LiftGroupDavhar::whereNotNull('code')->first();

        if (! $liftGroupDavhar) {
            $this->error('❌ No LiftGroupDavhar with CVSecurity code found for testing');
            $this->info('💡 Try running create test first');

            return;
        }

        $this->info("Updating LiftGroupDavhar ID: {$liftGroupDavhar->id} (Code: {$liftGroupDavhar->code})");

        try {
            // Find another toot for testing update
            $newToot = Toot::whereHas('davhar')
                ->whereHas('orc', function ($query) {
                    $query->whereNotNull('code');
                })
                ->where('id', '!=', $liftGroupDavhar->toot_id)
                ->with(['davhar', 'orc'])
                ->first();

            if (! $newToot) {
                $this->warn('⚠️ No different toot found, updating existing relationship');
                // Force an update by touching the model
                $liftGroupDavhar->touch();
            } else {
                $this->info("Changing toot from {$liftGroupDavhar->toot_id} to {$newToot->id}");
                $liftGroupDavhar->update(['toot_id' => $newToot->id]);
            }

            $this->info('✅ LiftGroupDavhar updated successfully');

        } catch (\Exception $e) {
            $this->error("❌ Failed to update LiftGroupDavhar: {$e->getMessage()}");
        }
    }

    protected function testDeleteLiftGroupDavhar()
    {
        $this->info('Testing LiftGroupDavhar deletion with CVSecurity integration...');

        // Find a lift group toot with a code
        $liftGroupDavhar = LiftGroupDavhar::whereNotNull('code')->first();

        if (! $liftGroupDavhar) {
            $this->error('❌ No LiftGroupDavhar with CVSecurity code found for testing');
            $this->info('💡 Try running create test first');

            return;
        }

        $code = $liftGroupDavhar->code;
        $id = $liftGroupDavhar->id;

        $this->info("Deleting LiftGroupDavhar ID: {$id} (Code: {$code})");

        try {
            $liftGroupDavhar->delete();
            $this->info('✅ LiftGroupDavhar deleted successfully');
            $this->info("✅ CVSecurity sync for code {$code} should be triggered");

        } catch (\Exception $e) {
            $this->error("❌ Failed to delete LiftGroupDavhar: {$e->getMessage()}");
        }
    }
}
