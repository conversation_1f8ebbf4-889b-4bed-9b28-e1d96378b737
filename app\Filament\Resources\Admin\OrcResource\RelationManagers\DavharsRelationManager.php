<?php

namespace App\Filament\Resources\Admin\OrcResource\RelationManagers;

use App\Models\Davhar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class DavharsRelationManager extends RelationManager
{
    protected static string $relationship = 'davhars';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(Davhar::NAME)
                    ->label('Давхарын нэр')
                    ->maxLength(30)
                    ->required(),

                Forms\Components\TextInput::make(Davhar::NUMBER)
                    ->label('Давхарын дугаар')
                    ->numeric()
                    ->minValue(-10)
                    ->maxValue(200)
                    ->required(),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NAME)->label('Давхарын нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)->label('Давхарын дугаар')->sortable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоотын тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('create')
                    ->label('Нэмэх')
                    ->icon('heroicon-s-plus')
                    ->url(fn (): string => route('filament.admin.resources.davhars.create', ['orc_id' => $this->getOwnerRecord()->id])),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn (Davhar $record): string => route('filament.admin.resources.davhars.edit', $record)),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
