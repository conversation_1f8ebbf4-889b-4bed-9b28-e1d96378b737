<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateQpayInvoiceRequest extends FormRequest
{
    const PARAMETER_PACKAGE_ID = 'package_id';

    const PARAMETER_KORPUS_ID = 'korpus_id';

    const PARAMETER_NUMBER = 'number';

    const PARAMETER_MONTH = 'month';

    const PARAMETER_MEMBERS = 'members';

    const PARAMETER_MEMBER = 'members.*';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'package_id' => 'required|exists:packages,id',
            'korpus_id' => 'required|exists:korpuses,id',
            'number' => [
                'required',
                Rule::exists('orshin_suugch_toots')->where(function ($query) {
                    $query->where('korpus_id', $this->korpus_id)
                        ->where('number', $this->number);
                }),
            ],
            'month' => 'required|integer',
            'members' => 'nullable|array',
            'members.*' => 'numeric|exists:orshin_suugches,phone',
        ];
    }
}
