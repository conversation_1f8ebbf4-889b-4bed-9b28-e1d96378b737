<?php

namespace App\Observers;

use App\Models\Orc;
use App\Services\OrcSyncService;
use Illuminate\Support\Facades\Log;

class OrcObserver
{
    protected OrcSyncService $orcSyncService;

    public function __construct(OrcSyncService $orcSyncService)
    {
        $this->orcSyncService = $orcSyncService;
    }

    /**
     * Handle the Orc "created" event.
     */
    public function created(Orc $orc): void
    {
        Log::info('OrcObserver: Orc created event triggered', [
            'orc_id' => $orc->id,
            'orc_number' => $orc->number,
        ]);

        // Sync with CVSecurity service
        $this->orcSyncService->syncCreate($orc);
    }

    /**
     * Handle the Orc "updated" event.
     */
    public function updated(Orc $orc): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($orc->wasChanged(Orc::CODE) && count($orc->getChanges()) === 1) {
            Log::debug('OrcObserver: Skipping sync for code-only update', [
                'orc_id' => $orc->id,
            ]);

            return;
        }

        Log::info('OrcObserver: Orc updated event triggered', [
            'orc_id' => $orc->id,
            'orc_number' => $orc->number,
            'changed_fields' => array_keys($orc->getChanges()),
        ]);

        // Sync with CVSecurity service
        $this->orcSyncService->syncUpdate($orc);
    }

    /**
     * Handle the Orc "deleted" event.
     */
    public function deleted(Orc $orc): void
    {
        Log::info('OrcObserver: Orc deleted event triggered', [
            'orc_id' => $orc->id,
            'orc_number' => $orc->number,
            'cv_code' => $orc->code,
        ]);

        // Sync with CVSecurity service
        $this->orcSyncService->syncDelete($orc);
    }

    /**
     * Handle the Orc "retrieved" event.
     * This can be used for read operations if needed.
     */
    public function retrieved(Orc $orc): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->orcSyncService->syncRead($orc);
    }
}
