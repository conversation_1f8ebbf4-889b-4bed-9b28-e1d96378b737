<?php

namespace Database\Factories;

use App\Models\Korpus;
use App\Models\Orc;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrcFactory extends Factory
{
    protected $model = Orc::class;

    public function definition(): array
    {
        return [
            'korpus_id' => Korpus::factory(),
            'number' => $this->faker->numberBetween(1, 4),
            'begin_toot_number' => 1,
            'end_toot_number' => 96,
        ];
    }
}
