<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\OrshinSuugchToot
 *
 * @mixin IdeHelperOrshinSuugchToot
 *
 * @property int $id
 * @property int $orshin_suugch_id
 * @property int|null $toot_id
 * @property string|null $access_code
 * @property string|null $ac_generated_date
 * @property string|null $state_bank_code
 * @property string|null $pin
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\OrshinSuugch $orshin_suugch
 * @property-read \App\Models\Toot|null $toot
 *
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereAcGeneratedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereAccessCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereStateBankCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class OrshinSuugchToot extends Model
{
    use HasFactory;

    const ID = 'id';

    const ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    const TOOT_ID = 'toot_id';

    const ACCESS_CODE = 'access_code';

    const AC_GENERATED_DATE = 'ac_generated_date';

    const STATE_BANK_CODE = 'state_bank_code';

    const PIN = 'pin';

    const RELATION_ORSHIN_SUUGCH = 'orshin_suugch';

    const RELATION_TOOT = 'toot';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'orshin_suugch_id',
        'toot_id',
        'access_code',
        'ac_generated_date',
        'state_bank_code',
        'pin',
    ];

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }

    public function bair(): BelongsTo
    {
        return $this->belongsTo(Bair::class);
    }

    public function orc(): BelongsTo
    {
        return $this->belongsTo(Orc::class);
    }

    public function toot(): BelongsTo
    {
        return $this->belongsTo(Toot::class);
    }
}
