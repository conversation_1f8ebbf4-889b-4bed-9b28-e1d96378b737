<?php

namespace App\Filament\Resources\Admin\OrshinSuugchImportResource\Pages;

use App\Exports\DefaultOsTemplate;
use App\Filament\Resources\Admin\OrshinSuugchImportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Maatwebsite\Excel\Facades\Excel;

class ListOrshinSuugchImports extends ListRecords
{
    protected static string $resource = OrshinSuugchImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download')->label('Загвар татах')->icon('heroicon-m-document-arrow-down')
                ->action(function () {
                    return Excel::download(new DefaultOsTemplate, 'os-template.xlsx');
                }),
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
