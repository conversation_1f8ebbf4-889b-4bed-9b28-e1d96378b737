<?php

namespace App\Filament\Resources\Admin;

use App\Exceptions\DeleteProtectionException;
use App\Filament\Resources\Admin\DavharResource\Pages;
use App\Filament\Resources\Admin\DavharResource\RelationManagers;
use App\Models\Davhar;
use App\Services\DoorNumberingService;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DavharResource extends Resource
{
    protected static ?string $model = Davhar::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationLabel = 'Давхарууд';

    protected static ?string $pluralModelLabel = 'Давхарууд';

    protected static ?string $modelLabel = 'давхар';

    protected static ?string $slug = 'davhars';

    // Hide from navigation - only accessible through Orc hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('korpus_info')
                            ->label('Блок')
                            ->content(function (?Davhar $record) {
                                if ($record && $record->korpus) {
                                    return "{$record->korpus->bair->name} - {$record->korpus->name}";
                                }

                                $korpusId = request()->get('korpus_id');
                                if ($korpusId) {
                                    $korpus = \App\Models\Korpus::with('bair')->find($korpusId);

                                    return $korpus ? "{$korpus->bair->name} - {$korpus->name}" : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Davhar::KORPUS_ID)
                            ->default(function () {
                                return request()->get('korpus_id');
                            }),

                        Forms\Components\TextInput::make(Davhar::NAME)
                            ->label('Давхарын нэр')
                            ->maxLength(30)
                            ->required(),

                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхарын дугаар')
                            ->numeric()
                            ->minValue(-10)
                            ->maxValue(200)
                            ->default(0)
                            ->required(),
                    ])
                    ->columns(2),

                // Auto-generation section
                Forms\Components\Section::make('Автомат тоот үүсгэх')
                    ->schema([
                        Forms\Components\Toggle::make('auto_generate')
                            ->label('Автомат тоот үүсгэх')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if (! $state) {
                                    // Clear auto-generation fields when disabled
                                    $set('numbering_type', null);
                                    $set('doors_per_floor', null);
                                    $set('digit_multiplier', null);
                                }
                            }),

                        Forms\Components\Select::make('numbering_type')
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                2 => 'Орц бүрээр (Type 2)',
                                3 => 'Давхар бүрээр (Type 3)',
                            ])
                            ->visible(fn (Get $get) => $get('auto_generate'))
                            ->reactive()
                            ->required(fn (Get $get) => $get('auto_generate')),

                        Forms\Components\TextInput::make('doors_per_floor')
                            ->label('Энэ давхарт байх тоотын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get('auto_generate'))
                            ->required(fn (Get $get) => $get('auto_generate')),

                        Forms\Components\Select::make('digit_multiplier')
                            ->label('Цифрийн үржүүлэгч')
                            ->options([
                                10 => '10 (2 оронтой: 11, 12, 13...)',
                                100 => '100 (3 оронтой: 101, 102, 103...)',
                                1000 => '1000 (4 оронтой: 1001, 1002, 1003...)',
                            ])
                            ->visible(fn (Get $get) => $get('auto_generate') && $get('numbering_type') == 3)
                            ->required(fn (Get $get) => $get('auto_generate') && $get('numbering_type') == 3),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('generate')
                                ->label('Тоот үүсгэх')
                                ->color('success')
                                ->icon('heroicon-o-plus-circle')
                                ->visible(fn (Get $get) => $get('auto_generate'))
                                ->requiresConfirmation()
                                ->modalHeading('Тоот үүсгэх')
                                ->modalDescription('Энэ үйлдэл нь одоо байгаа бүх тоотуудыг устгаад шинээр үүсгэнэ. Та итгэлтэй байна уу?')
                                ->modalSubmitActionLabel('Тийм, үүсгэх')
                                ->action(function (Davhar $record, Get $get) {
                                    try {
                                        // Get current form state
                                        $autoGenerate = $get('auto_generate');
                                        $numberingType = $get('numbering_type');
                                        $doorsPerFloor = $get('doors_per_floor');
                                        $digitMultiplier = $get('digit_multiplier');

                                        // Validate form data before proceeding
                                        if (! $autoGenerate) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэх сонголтыг идэвхжүүлнэ үү.')
                                                ->danger()
                                                ->send();

                                            return;
                                        }

                                        $doorNumberingService = app(DoorNumberingService::class);

                                        $doorNumberingService->generateDavharToots($record, (int) $numberingType, (int) $doorsPerFloor, $digitMultiplier ? (int) $digitMultiplier : null);

                                        Notification::make()
                                            ->title('Амжилттай үүсгэлээ')
                                            ->body("Давхар {$record->name}-д {$doorsPerFloor} тоот үүсгэлээ.")
                                            ->success()
                                            ->send();

                                        return redirect(self::getUrl('edit', ['record' => $record]));
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Алдаа гарлаа')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ]),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->hidden(fn (?Davhar $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Давхарууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NAME)->label('Давхарын нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)->label('Давхарын дугаар')->sortable(),

                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоотын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function (Model $record) {
                        try {
                            $record->delete();
                        } catch (DeleteProtectionException $e) {
                            Notification::make()
                                ->title('Delete Failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();

                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $failedDeletions = 0;

                            foreach ($records as $record) {
                                try {
                                    $record->delete();
                                } catch (DeleteProtectionException $e) {
                                    $failedDeletions++;
                                }
                            }

                            if ($failedDeletions > 0) {
                                Notification::make()
                                    ->title('Some Deletions Failed')
                                    ->body("$failedDeletions record(s) could not be deleted because they have related toots.")
                                    ->danger()
                                    ->send();
                            }
                        }),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TootsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDavhars::route('/'),
            'create' => Pages\CreateDavhar::route('/create'),
            'edit' => Pages\EditDavhar::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();

        return parent::getEloquentQuery()->whereHas('korpus.bair.sukh', function (Builder $query) use ($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
