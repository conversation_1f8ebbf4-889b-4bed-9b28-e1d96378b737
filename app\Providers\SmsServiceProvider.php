<?php

namespace App\Providers;

use App\Interfaces\CallProSmsInterface;
use App\Services\CallProSmsServiceClient;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class SmsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(CallProSmsInterface::class, function (Application $app) {
            return new CallProSmsServiceClient;
        });
    }
}
