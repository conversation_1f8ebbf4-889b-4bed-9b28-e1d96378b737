<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ContractTemplateType
 *
 * @mixin IdeHelperContractTemplateType
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplateType whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ContractTemplateType extends Model
{
    use HasFactory;

    const ID = 'id';

    const NAME = 'name';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
    ];
}
