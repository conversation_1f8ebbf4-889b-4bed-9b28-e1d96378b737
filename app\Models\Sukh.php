<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * @mixin IdeHelperSukh
 */
class Sukh extends Model
{
    use HasFactory;

    const ID = 'id';

    const NAME = 'name';

    const REGISTRATION_NUMBER = 'registration_number';

    const PHONE = 'phone';

    const EMAIL = 'email';

    const AIMAG_ID = 'aimag_id';

    const SOUM_ID = 'soum_id';

    const BAG_ID = 'bag_id';

    const CODE = 'code';

    const RELATION_BAIRS = 'bairs';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::REGISTRATION_NUMBER,
        self::PHONE,
        self::EMAIL,
        self::AIMAG_ID,
        self::SOUM_ID,
        self::BAG_ID,
        self::CODE,
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_sukh');
    }

    public function bairs(): HasMany
    {
        return $this->hasMany(Bair::class);
    }

    public function korpuses(): HasManyThrough
    {
        return $this->hasManyThrough(Korpus::class, Bair::class);
    }

    public function orcs(): HasManyThrough
    {
        return $this->hasManyThrough(Orc::class, Korpus::class, 'bair_id', 'korpus_id', 'id', 'id')
            ->join('bairs', 'bairs.id', '=', 'korpuses.bair_id')
            ->where('bairs.sukh_id', $this->id)
            ->select('orcs.*');
    }

    public function aimag(): BelongsTo
    {
        return $this->belongsTo(Aimag::class);
    }

    public function soum(): BelongsTo
    {
        return $this->belongsTo(Soum::class);
    }

    public function bag(): BelongsTo
    {
        return $this->belongsTo(Bag::class);
    }

    public function orshin_suugches(): HasMany
    {
        return $this->hasMany(OrshinSuugch::class);
    }
}
