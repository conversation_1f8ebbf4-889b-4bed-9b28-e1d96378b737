<?php

namespace App\Filament\Resources\Admin\OrcResource\Pages;

use App\Exceptions\DeleteProtectionException;
use App\Filament\Resources\Admin\OrcResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditOrc extends EditRecord
{
    protected static string $resource = OrcResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function () {
                    try {
                        $this->record->delete();

                        return redirect()->to($this->getResource()::getUrl('index'));
                    } catch (DeleteProtectionException $e) {
                        Notification::make()
                            ->title('Delete Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                }),
        ];
    }

    public function getBreadcrumbs(): array
    {
        $record = $this->getRecord();

        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            url()->route('filament.admin.resources.bairs.edit', $record->korpus->bair_id) => $record->korpus->bair->name,
            url()->route('filament.admin.resources.korpuses.edit', $record->korpus_id) => "Блок: {$record->korpus->name}",
            '#' => "Орц: {$record->number}",
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
