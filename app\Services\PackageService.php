<?php

namespace App\Services;

use App\Models\Korpus;
use App\Models\Package;
use Illuminate\Database\Eloquent\Builder;

class PackageService
{
    public function __construct() {}

    public function getPackageForNewOs($korpusId)
    {
        $korpus = Korpus::find($korpusId);
        $sukhId = $korpus->bair->sukh->id;
        if (! isset($sukhId)) {
            return;
        }
        $package = Package::where(Package::IS_FREE, true)->where(Package::IS_NEW_OS_ERKH, true)
            ->whereHas(Package::RELATION_SUKHS, function (Builder $query) use ($sukhId) {
                $query->where('sukh_id', intval($sukhId));
            })->first();
        if (! isset($package)) {
            $package = Package::doesntHave(Package::RELATION_SUKHS)->where(Package::IS_FREE, true)->where(Package::IS_NEW_OS_ERKH, true)->first();
        }

        return $package;
    }

    public function setNewOsErkhForPackage($sukhs, $packageId)
    {
        if (isset($sukhs) && count($sukhs) > 0) {
            foreach ($sukhs as $key => $sukhId) {
                Package::whereHas(Package::RELATION_SUKHS, function (Builder $query) use ($sukhId) {
                    $query->where('sukh_id', intval($sukhId));
                })
                    ->where(Package::ID, '!=', $packageId)
                    ->update([Package::IS_NEW_OS_ERKH => false]);
            }
        } else {
            Package::where(Package::ID, '!=', $packageId)->doesntHave(Package::RELATION_SUKHS)->update([Package::IS_NEW_OS_ERKH => false]);
        }
    }

    public function getTotalPrice($packageId, $month, $memberCount)
    {
        $package = Package::find($packageId);
        $packageMonth = $package->package_months->first(function ($item) use ($month) {
            return $item->value <= $month;
        });
        $packageMember = $package->package_members->first(function ($item) use ($memberCount) {
            return $item->value <= $memberCount;
        });

        $monthDiscountPercent = isset($packageMonth) ? $packageMonth->discount : 0;
        $memberDiscountPercent = isset($packageMember) ? $packageMember->discount : 0;
        $price = $package->price * $month * $memberCount;
        $monthDiscount = $price * $monthDiscountPercent / 100;
        $memberDiscount = ($price - $monthDiscount) * $memberDiscountPercent / 100;
        $totalPrice = $price - $monthDiscount - $memberDiscount;

        return ['price' => $price, 'month_discount' => $monthDiscount, 'member_discount' => $memberDiscount, 'total_price' => $totalPrice];
    }
}
