<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LiftGroupDeviceDavharToot>
 */
class LiftGroupDeviceDavharTootFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'lift_group_davhar_id' => \App\Models\LiftGroupDavhar::factory(),
            'toot_id' => \App\Models\Toot::factory(),
        ];
    }
}
