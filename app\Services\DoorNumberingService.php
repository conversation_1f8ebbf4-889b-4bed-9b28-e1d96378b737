<?php

namespace App\Services;

use App\Models\Davhar;
use App\Models\Korpus;
use App\Models\Toot;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DoorNumberingService
{
    /**
     * Generate Davhars and Toots for an entire Korpus.
     * The new hierarchy is: Korpus -> (Orcs, Davhars), Orc -> Toot, Toot -> Davhar
     *
     * @param  int|null  $numberOfFloors  Total floors in the Korpus
     *
     * @throws Exception
     */
    public function generateKorpusHierarchy(Korpus $korpus, int $numberingType, int $doorsPerFloor, bool $generateFloors, ?int $numberOfFloors, ?int $digitMultiplier): void
    {
        if (! in_array($numberingType, [1, 3])) {
            throw new Exception('Invalid numbering type. Only Type 1 (Korpus-wise) and 3 (Floor-wise) are supported.');
        }

        if ($numberingType === 3 && empty($digitMultiplier)) {
            throw new Exception('Digit multiplier is required for floor-wise (Type 3) numbering.');
        }

        if ($korpus->orcs()->count() === 0) {
            throw new Exception('Orcs must be created before enabling auto-generation.');
        }

        DB::transaction(function () use ($korpus, $numberingType, $doorsPerFloor, $generateFloors, $numberOfFloors, $digitMultiplier) {
            // 1. Delete existing Toots for the entire Korpus
            $korpus->toots()->delete();

            $davhars = [];

            if ($generateFloors) {
                if (! $numberOfFloors || $numberOfFloors <= 0) {
                    throw new Exception('Number of floors must be a positive integer when generating new floors.');
                }
                // Also delete existing Davhars for the Korpus
                $korpus->davhars()->delete();

                // 2. Generate Davhars for the Korpus
                for ($i = 1; $i <= $numberOfFloors; $i++) {
                    $davhars[] = Davhar::create([
                        'korpus_id' => $korpus->id,
                        'name' => "{$i} давхар",
                        'number' => $i,
                    ]);
                }
            } else {
                $davhars = $korpus->davhars()->orderBy('number')->get()->all();
                if (empty($davhars)) {
                    throw new Exception('No existing floors found. Enable floor generation or create floors manually.');
                }
            }

            // 3. Generate Toots based on numbering type
            switch ($numberingType) {
                case 1: // Korpus-wise
                    $this->generateKorpusWise($korpus, $davhars, $doorsPerFloor);
                    break;
                case 3: // Floor-wise
                    $this->generateFloorWise($korpus, $davhars, $doorsPerFloor, $digitMultiplier);
                    break;
            }
        });

        Log::info('Korpus hierarchy generated successfully', [
            'korpus_id' => $korpus->id,
            'numbering_type' => $numberingType,
            'orcs_count' => $korpus->orcs->count(),
            'floors' => $generateFloors ? $numberOfFloors : $korpus->davhars()->count(),
            'doors_per_floor' => $doorsPerFloor,
            'generated_floors' => $generateFloors,
        ]);
    }

    /**
     * Type 1: Korpus-wise Consecutive Numbering
     * Doors are numbered consecutively across all orcs and floors.
     * Example for 2 Orcs, 2 Floors, 3 doors per floor:
     * Orc 1: Floor 1: 1-3, Floor 2: 7-9
     * Orc 2: Floor 1: 4-6, Floor 2: 10-12
     *
     * @param  Davhar[]  $davhars
     */
    private function generateKorpusWise(Korpus $korpus, array $davhars, int $doorsPerFloor): void
    {
        $doorNumber = 1;
        $orcs = $korpus->orcs()->orderBy('number')->get();
        $totalFloorsPerOrc = count($davhars);

        foreach ($orcs as $orc) {
            foreach ($davhars as $davhar) {
                // Generate all doors for this floor in this Orc
                for ($i = 1; $i <= $doorsPerFloor; $i++) {
                    Toot::create([
                        'number' => $doorNumber++,
                        'korpus_id' => $korpus->id,
                        'orc_id' => $orc->id,
                        'davhar_id' => $davhar->id,
                    ]);
                }
            }
        }
    }

    /**
     * Type 3: Floor-wise Consecutive Numbering
     * Door numbers include the floor number as a prefix.
     * Example for 2 Orcs, Floor 1, 3 doors per floor, multiplier 100:
     * Orc 1: 101, 102, 103
     * Orc 2: 101, 102, 103
     *
     * @param  Davhar[]  $davhars
     */
    private function generateFloorWise(Korpus $korpus, array $davhars, int $doorsPerFloor, int $digitMultiplier): void
    {
        $orcs = $korpus->orcs()->orderBy('number')->get();

        foreach ($davhars as $davhar) {
            $floorBase = ($davhar->number * $digitMultiplier);

            foreach ($orcs as $orc) {
                // Generate all doors for this Orc on this floor
                for ($i = 1; $i <= $doorsPerFloor; $i++) {
                    $doorNumber = $floorBase + $i;
                    Toot::create([
                        'number' => $doorNumber,
                        'korpus_id' => $korpus->id,
                        'orc_id' => $orc->id,
                        'davhar_id' => $davhar->id,
                    ]);
                }
            }
        }
    }

    /**
     * Generate Toots for a specific Davhar
     * Used by DavharResource for individual floor door generation
     */
    public function generateDavharToots(Davhar $davhar, int $numberingType, int $doorsPerFloor, ?int $digitMultiplier = null): void
    {
        if (! in_array($numberingType, [2, 3])) {
            throw new Exception('Invalid numbering type. Only Type 2 (Entrance-wise) and 3 (Floor-wise) are supported for individual davhar generation.');
        }

        if ($numberingType === 3 && empty($digitMultiplier)) {
            throw new Exception('Digit multiplier is required for floor-wise (Type 3) numbering.');
        }

        $korpus = $davhar->korpus;
        if ($korpus->orcs()->count() === 0) {
            throw new Exception('Orcs must be created before generating toots for this floor.');
        }

        DB::transaction(function () use ($davhar, $numberingType, $doorsPerFloor, $digitMultiplier) {
            // Delete existing toots for this davhar
            $davhar->toots()->delete();

            $orcs = $davhar->korpus->orcs()->orderBy('number')->get();

            switch ($numberingType) {
                case 2: // Entrance-wise (Type 2)
                    $this->generateDavharEntranceWise($davhar, $orcs, $doorsPerFloor);
                    break;
                case 3: // Floor-wise (Type 3)
                    $this->generateDavharFloorWise($davhar, $orcs, $doorsPerFloor, $digitMultiplier);
                    break;
            }
        });

        Log::info('Davhar toots generated successfully', [
            'davhar_id' => $davhar->id,
            'davhar_name' => $davhar->name,
            'numbering_type' => $numberingType,
            'doors_per_floor' => $doorsPerFloor,
            'orcs_count' => $davhar->korpus->orcs->count(),
        ]);
    }

    /**
     * Type 2: Entrance-wise numbering for a single davhar
     * Doors are numbered consecutively within each entrance
     */
    private function generateDavharEntranceWise(Davhar $davhar, $orcs, int $doorsPerFloor): void
    {
        foreach ($orcs as $orc) {
            for ($i = 1; $i <= $doorsPerFloor; $i++) {
                Toot::create([
                    'number' => $i,
                    'korpus_id' => $davhar->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
            }
        }
    }

    /**
     * Type 3: Floor-wise numbering for a single davhar
     * Door numbers include the floor number as a prefix
     */
    private function generateDavharFloorWise(Davhar $davhar, $orcs, int $doorsPerFloor, int $digitMultiplier): void
    {
        $floorBase = ($davhar->number * $digitMultiplier);

        foreach ($orcs as $orc) {
            for ($i = 1; $i <= $doorsPerFloor; $i++) {
                $doorNumber = $floorBase + $i;
                Toot::create([
                    'number' => $doorNumber,
                    'korpus_id' => $davhar->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
            }
        }
    }
}
