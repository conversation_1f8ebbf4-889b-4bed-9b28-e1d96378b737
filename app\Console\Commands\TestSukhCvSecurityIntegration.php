<?php

namespace App\Console\Commands;

use App\Models\Sukh;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use App\Services\SukhSyncService;
use Illuminate\Console\Command;

class TestSukhCvSecurityIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:sukh-cv-security {action=status : Action to perform (status|create|update|delete)}';

    /**
     * The console description of the command.
     *
     * @var string
     */
    protected $description = 'Test Sukh CVSecurity integration';

    protected CvSecurityServiceExt $cvSecurityService;

    protected SukhSyncService $sukhSyncService;

    public function __construct(CvSecurityServiceExt $cvSecurityService, SukhSyncService $sukhSyncService)
    {
        parent::__construct();
        $this->cvSecurityService = $cvSecurityService;
        $this->sukhSyncService = $sukhSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->checkServiceStatus();
                break;
            case 'create':
                $this->testCreateSukh();
                break;
            case 'update':
                $this->testUpdateSukh();
                break;
            case 'delete':
                $this->testDeleteSukh();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: status, create, update, delete');

                return 1;
        }

        return 0;
    }

    protected function checkServiceStatus()
    {
        $this->info('Checking CVSecurity service status...');

        $status = $this->cvSecurityService->getServiceStatus();

        $this->table(
            ['Property', 'Value'],
            [
                ['Service', $status['service']],
                ['Status', $status['status']],
                ['Host', $status['host']],
                ['Port', $status['port']],
            ]
        );

        if ($status['status'] === 'online') {
            $this->info('✅ CVSecurity service is available');
        } else {
            $this->error('❌ CVSecurity service is not available');
        }
    }

    protected function testCreateSukh()
    {
        $this->info('Testing Sukh creation with CVSecurity integration...');

        // Create a test Sukh
        $sukh = Sukh::create([
            'name' => 'Test СӨХ '.now()->format('Y-m-d H:i:s'),
            'registration_number' => 'T'.rand(100000, 999999),
            'phone' => '9'.rand(1000000, 9999999),
            'email' => 'test'.rand(1000, 9999).'@example.com',
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ]);

        $this->info("Created Sukh with ID: {$sukh->id}");

        // Wait a moment for the observer to process
        sleep(2);

        // Refresh the model to get the updated code
        $sukh->refresh();

        if ($sukh->code) {
            $this->info("✅ CVSecurity code assigned: {$sukh->code}");
        } else {
            $this->warn('⚠️ No CVSecurity code assigned (service might be unavailable)');
        }

        $this->table(
            ['Field', 'Value'],
            [
                ['ID', $sukh->id],
                ['Name', $sukh->name],
                ['Registration Number', $sukh->registration_number],
                ['Phone', $sukh->phone],
                ['Email', $sukh->email],
                ['CV Security Code', $sukh->code ?: 'Not assigned'],
            ]
        );
    }

    protected function testUpdateSukh()
    {
        $this->info('Testing Sukh update with CVSecurity integration...');

        // Find the latest test Sukh
        $sukh = Sukh::where('name', 'like', 'Test СӨХ%')->latest()->first();

        if (! $sukh) {
            $this->error('No test Sukh found. Run "create" action first.');

            return;
        }

        $this->info("Updating Sukh ID: {$sukh->id}");

        // Update the Sukh
        $sukh->update([
            'name' => $sukh->name.' (Updated)',
            'phone' => '9'.rand(1000000, 9999999),
        ]);

        $this->info('✅ Sukh updated successfully');

        // Wait a moment for the observer to process
        sleep(2);

        // Refresh the model
        $sukh->refresh();

        $this->table(
            ['Field', 'Value'],
            [
                ['ID', $sukh->id],
                ['Name', $sukh->name],
                ['Phone', $sukh->phone],
                ['CV Security Code', $sukh->code ?: 'Not assigned'],
            ]
        );
    }

    protected function testDeleteSukh()
    {
        $this->info('Testing Sukh deletion with CVSecurity integration...');

        // Find the latest test Sukh
        $sukh = Sukh::where('name', 'like', 'Test СӨХ%')->latest()->first();

        if (! $sukh) {
            $this->error('No test Sukh found. Run "create" action first.');

            return;
        }

        $sukhId = $sukh->id;
        $sukhCode = $sukh->code;

        $this->info("Deleting Sukh ID: {$sukhId} with CV Code: ".($sukhCode ?: 'None'));

        // Delete the Sukh
        $sukh->delete();

        $this->info('✅ Sukh deleted successfully');
        $this->info('CVSecurity deletion sync should have been triggered automatically');
    }
}
