<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LiftGroupDeviceDavhar>
 */
class LiftGroupDeviceDavharFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'lift_group_device_id' => \App\Models\LiftGroup::factory(),
            'davhar_id' => \App\Models\Davhar::factory(),
            'code' => $this->faker->optional()->regexify('[A-Z0-9]{8}'),
            'offset' => $this->faker->numberBetween(-5, 5),
            'enabled' => $this->faker->boolean(),
        ];
    }
}
