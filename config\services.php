<?php

use App\Models\Constant\ConstData;

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'callpro' => [
        'host' => env('CALLPRO_HOST'),
        'key' => env('CALLPRO_KEY'),
        'phone_number' => env('CALLPRO_PHONE_NUMBER'),
    ],

    'main' => [
        'app_env' => env(ConstData::APP_ENV),
        'app_url' => env(ConstData::APP_URL),
        'db_database' => env(ConstData::DB_DATABASE),
    ],

    'qpay' => [
        'invoice_code' => env(ConstData::QPAY_INVOICE_CODE),
        'host' => env(ConstData::QPAY_HOST),
        'username' => env(ConstData::QPAY_USERNAME),
        'password' => env(ConstData::QPAY_PASSWORD),
    ],

    'chirp' => [
        'tenant_name' => env(ConstData::CHIRP_STACK_TENANT_NAME),
        'app_name' => env(ConstData::CHIRP_STACK_APP_NAME),
        'host' => env(ConstData::CHIRP_STACK_HOST),
        'token' => env(ConstData::CHIRP_STACK_TOKEN),
    ],

    'bpay' => [
        'url' => env(ConstData::BPAY_URL),
        'user_name' => env(ConstData::BPAY_USER_NAME),
        'password' => env(ConstData::BPAY_PASSWORD),
        'our_email' => env(ConstData::BPAY_OUR_EMAIL),
    ],

    'cv_security' => [
        'host' => env(ConstData::CV_SECURITY_HOST),
        'port' => env(ConstData::CV_SECURITY_PORT),
        'api_key' => env(ConstData::CV_SECURITY_API_KEY),
        'use_https' => env('CV_SECURITY_USE_HTTPS', true),
        'verify_ssl' => env('CV_SECURITY_VERIFY_SSL', false),
        'sync_enabled' => env('CV_SECURITY_SYNC_ENABLED', true),
    ],

    'cv_security_ext' => [
        'host' => env(ConstData::CV_SECURITY_EXT_HOST),
        'port' => env(ConstData::CV_SECURITY_EXT_PORT),
        'api_key' => env(ConstData::CV_SECURITY_API_KEY),
    ],
];
