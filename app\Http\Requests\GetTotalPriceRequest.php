<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetTotalPriceRequest extends FormRequest
{
    const PARAMETER_PACKAGE_ID = 'package_id';

    const PARAMETER_MONTH = 'month';

    const PARAMETER_MEMBER_COUNT = 'member_count';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_PACKAGE_ID => 'required|exists:packages,id',
            self::PARAMETER_MONTH => 'required|integer',
            self::PARAMETER_MEMBER_COUNT => 'required|integer',
        ];
    }
}
