<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BpayCheckInvoice extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'status' => $this->invoiceId,
            /**
             * 1000 - Төлөгдөөгүй,
             * 1001 - Төлөгдсөн,
             * 1002 - Хэрэглэгч нэхэмжлэхийг цуцалсан,
             */
            'statusCode' => $this->qr_text,
            'statusSystem' => $this->qr_image,
        ];
    }
}
