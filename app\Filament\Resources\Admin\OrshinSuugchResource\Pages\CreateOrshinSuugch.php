<?php

namespace App\Filament\Resources\Admin\OrshinSuugchResource\Pages;

use App\Filament\Resources\Admin\OrshinSuugchResource;
use App\Models\OrshinSuugch;
use App\Services\BPayService;
use App\Services\ToolService;
use App\Services\UserInfoService;
use Filament\Resources\Pages\CreateRecord;

class CreateOrshinSuugch extends CreateRecord
{
    protected static string $resource = OrshinSuugchResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        $data[OrshinSuugch::IS_ADMIN] = true;
        $data[OrshinSuugch::SUKH_ID] = $sukh->id;

        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function afterCreate(): void
    {
        // $orshinSuugch->uniq_code = resolve(ToolService::class)->generateCodeFromDatetime();
        // resolve(BPayService::class)->createBpayUser($orshinSuugch);
    }
}
