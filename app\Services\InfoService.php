<?php

namespace App\Services;

use App\Models\Aimag;
use App\Models\Bag;
use App\Models\ColumnName;
use App\Models\Soum;
use Illuminate\Support\Facades\DB;

class InfoService
{
    public function __construct() {}

    public function getTableNamesInContract()
    {
        $orignTableName = 'contracts';
        $columnNamesHasRelation = [$orignTableName => $orignTableName];
        $columnNames = $this->getColumnsByRelation($orignTableName, true);
        foreach ($columnNames as $key => $columnName) {
            $tableName = str_replace('_id', 's', $columnName);
            $addedTableName = $orignTableName.'.'.$tableName;
            $columnNamesHasRelation[$addedTableName] = $addedTableName;
            $subColumnNames = $this->getColumnsByRelation($tableName, true);
            foreach ($subColumnNames as $key => $subColumnName) {
                $subTableName = str_replace('_id', 's', $subColumnName);
                $subAddedTableName = $addedTableName.".$subTableName";
                $columnNamesHasRelation[$subAddedTableName] = $subAddedTableName;
            }
        }

        return $columnNamesHasRelation;
    }

    public function getColumnNames($tableName)
    {
        if (! isset($tableName)) {
            return [];
        }
        $schema = config('services.main.db_database');

        return collect(DB::select('SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?', [$schema, $tableName]))->pluck('COLUMN_NAME');
    }

    public function getColumnsByRelation($tableName, $hasRelation = false)
    {
        $columnsHasRelation = [];
        $columnNames = $this->getColumnNames($tableName);
        foreach ($columnNames as $key => $columnName) {
            if ($hasRelation ? ! str_contains($columnName, '_id') : str_contains($columnName, '_id') || $columnName == 'id') {
                continue;
            }
            $columnsHasRelation[] = $columnName;
        }

        return $columnsHasRelation;
    }

    public function getColumnNamesByTableId($tableId)
    {
        if (! isset($tableId)) {
            return [];
        }

        return ColumnName::where(ColumnName::TABLE_NAME_ID, $tableId)->get()->pluck('name', 'name');
    }

    public function getSoums($aimag_id)
    {
        return Soum::where(Soum::AIMAG_ID, $aimag_id)->get()->pluck(Soum::NAME, Soum::ID);
    }

    public function getBags($aimag_id, $soum_id)
    {
        return Bag::where(Bag::AIMAG_ID, $aimag_id)->where(Bag::SOUM_ID, $soum_id)->get()->pluck(Bag::NAME, Bag::ID);
    }

    public function getUBId()
    {
        $ub = Aimag::where(Aimag::NAME, 'Улаанбаатар')->first();

        return $ub ? $ub->id : null;
    }
}
