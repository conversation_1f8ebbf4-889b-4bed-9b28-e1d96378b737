<?php

use App\Models\DeviceDtl;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_dtls', function (Blueprint $table) {
            $table->id();
            $table->foreignId(DeviceDtl::DEVICE_HDR_ID)->constrained('device_hdrs')->cascadeOnDelete();
            $table->string(DeviceDtl::NAME);
            $table->string(DeviceDtl::DEV_EUI);
            $table->string(DeviceDtl::JOIN_EUI);
            $table->string(DeviceDtl::NWK_KEY);
            $table->foreignId('korpus_id')->nullable()->constrained(); // Changed from bair_id
            $table->foreignId(DeviceDtl::ORC_ID);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('device_dtls');
    }
};
