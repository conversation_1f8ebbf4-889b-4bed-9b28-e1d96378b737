<?php

namespace Database\Factories;

use App\Models\Bair;
use App\Models\Korpus;
use Illuminate\Database\Eloquent\Factories\Factory;

class KorpusFactory extends Factory
{
    protected $model = Korpus::class;

    public function definition(): array
    {
        return [
            'bair_id' => Bair::factory(),
            'name' => $this->faker->randomElement(['A', 'B', 'C', '1', '2', '3']),
        ];
    }
}
