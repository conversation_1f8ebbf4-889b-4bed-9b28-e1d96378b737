<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Contract
 *
 * @mixin IdeHelperContract
 *
 * @property int $id
 * @property int $sukh_id
 * @property int $contract_template_id
 * @property int|null $zuuchlagch_id
 * @property string $contract_no
 * @property string $begin_date
 * @property string $end_date
 * @property string $amount
 * @property string $khariltsagch_position
 * @property string $khariltsagch_signa
 * @property string|null $file_path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ContractTemplate|null $contract_template
 * @property-read \App\Models\Sukh|null $sukh
 * @property-read \App\Models\Zuuchlagch|null $zuuchlagch
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract query()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereBeginDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereContractNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereContractTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereKhariltsagchPosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereKhariltsagchSigna($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereSukhId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereZuuchlagchId($value)
 *
 * @mixin \Eloquent
 */
class Contract extends Model
{
    use HasFactory;

    const ID = 'id';

    const SUKH_ID = 'sukh_id';

    const CONTRACT_TEMPLATE_ID = 'contract_template_id';

    const ZUUCHLAGCH_ID = 'zuuchlagch_id';

    const BEGIN_DATE = 'begin_date';

    const END_DATE = 'end_date';

    const CONTRACT_NO = 'contract_no';

    const AMOUNT = 'amount';

    const KHARILTSAGCH_POSITION = 'khariltsagch_position';

    const KHARILTSAGCH_SIGNA = 'khariltsagch_signa';

    const FILE_PATH = 'file_path';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::SUKH_ID,
        self::CONTRACT_TEMPLATE_ID,
        self::ZUUCHLAGCH_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::CONTRACT_NO,
        self::AMOUNT,
        self::KHARILTSAGCH_POSITION,
        self::KHARILTSAGCH_SIGNA,
        self::FILE_PATH,
    ];

    public function sukh(): BelongsTo
    {
        return $this->belongsTo(Sukh::class);
    }

    public function contract_template(): BelongsTo
    {
        return $this->belongsTo(ContractTemplate::class);
    }

    public function zuuchlagch(): BelongsTo
    {
        return $this->belongsTo(Zuuchlagch::class);
    }
}
