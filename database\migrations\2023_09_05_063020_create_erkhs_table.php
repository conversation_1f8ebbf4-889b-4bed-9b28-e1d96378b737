<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('erkhs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('orshin_suugch_id');
            $table->foreignId('bair_id');
            $table->foreignId('invoice_id');
            $table->date('begin_date');
            $table->date('end_date');
            $table->string('products')->nullable(); // Replaced product enum
            $table->boolean('removed_device_code')->default(false);
            $table->unsignedBigInteger('number')->nullable();
            $table->unsignedBigInteger('korpus_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erkhs');
    }
};
