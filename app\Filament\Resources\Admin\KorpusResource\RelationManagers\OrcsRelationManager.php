<?php

namespace App\Filament\Resources\Admin\KorpusResource\RelationManagers;

use App\Models\Orc;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class OrcsRelationManager extends RelationManager
{
    protected static string $relationship = 'orcs';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(Orc::NUMBER)
                    ->label('Орцны дугаар')
                    ->unique(
                        ignoreRecord: true,
                        modifyRuleUsing: function (Unique $rule) {
                            $korpusId = $this->getOwnerRecord()->id;

                            return $rule->where('korpus_id', $korpusId);
                        }
                    )
                    ->numeric()
                    ->required(),

                Forms\Components\TextInput::make(Orc::CODE)
                    ->label('CV Security код')
                    ->disabled()
                    ->dehydrated(false)
                    ->helperText('Энэ талбар автоматаар CV Security системээс ирнэ'),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Orc::NUMBER)->label('Орцны дугаар')->sortable(),
                Tables\Columns\TextColumn::make(Orc::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоотын тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('create')
                    ->label('Нэмэх')
                    ->icon('heroicon-s-plus')
                    ->url(fn (): string => route('filament.admin.resources.orcs.create', ['korpus_id' => $this->getOwnerRecord()->id])),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn (Orc $record): string => route('filament.admin.resources.orcs.edit', $record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
