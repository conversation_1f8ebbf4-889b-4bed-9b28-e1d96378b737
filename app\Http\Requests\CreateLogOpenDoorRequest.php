<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateLogOpenDoorRequest extends FormRequest
{
    const PARAMETER_ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    const PARAMETER_DEV_EUI = 'dev_eui';

    const PARAMETER_PRODUCT = 'product';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_ORSHIN_SUUGCH_ID => 'required|exists:orshin_suugches,id',
            self::PARAMETER_DEV_EUI => 'required|string',
            self::PARAMETER_PRODUCT => 'required|string',
        ];
    }
}
