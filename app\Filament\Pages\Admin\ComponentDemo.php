<?php

namespace App\Filament\Pages\Admin;

use App\Filament\Components\TransferList;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class ComponentDemo extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-beaker';

    protected static string $view = 'filament.pages.admin.component-demo';

    protected static ?string $navigationGroup = 'Demo';

    public ?array $data = [];

    public static function getNavigationLabel(): string
    {
        return 'Component Demo';
    }

    public function getTitle(): string|Htmlable
    {
        return 'Component Demo';
    }

    public static function shouldRegisterNavigation(): bool
    {
        return in_array(config('app.env'), ['local', 'dev']);
    }

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('TransferList Component')
                    ->description('Custom dual-column transfer list component with 4 transfer buttons')
                    ->schema([
                        TransferList::make('user_group_assignments')
                            ->leftTitle('Available Users')
                            ->rightTitle('Assigned Groups')
                            ->height(250)
                            ->availableItems([
                                ['id' => 1, 'name' => 'Filament Examples'],
                                ['id' => 2, 'name' => 'Mr. Johnny Fritsch DDS'],
                                ['id' => 3, 'name' => 'Dr. Eunice Blick'],
                                ['id' => 4, 'name' => 'Hershel Franecki II'],
                                ['id' => 5, 'name' => 'Connie Schumm'],
                                ['id' => 6, 'name' => 'Brandi Feest Sr.'],
                                ['id' => 7, 'name' => 'Nico Russel'],
                            ])
                            ->selectedItems([
                                ['id' => 101, 'name' => 'USA Branch'],
                                ['id' => 102, 'name' => 'UK Branch'],
                                ['id' => 103, 'name' => 'Canada Branch'],
                                ['id' => 104, 'name' => 'Australia Branch'],
                                ['id' => 105, 'name' => 'New Zealand Branch'],
                                ['id' => 106, 'name' => 'South Africa Branch'],
                                ['id' => 107, 'name' => 'Singapore Branch'],
                            ]),
                    ]),

                Section::make('Generic TransferList Component')
                    ->description('Example with different field names and entities')
                    ->schema([
                        TransferList::make('product_category_assignments')
                            ->leftTitle('Available Products')
                            ->rightTitle('Selected Categories')
                            ->keyField('product_id')
                            ->labelField('title')
                            ->height(150)
                            ->availableItems([
                                ['product_id' => 'p1', 'title' => 'Laravel Framework'],
                                ['product_id' => 'p2', 'title' => 'Filament Admin Panel'],
                                ['product_id' => 'p3', 'title' => 'Alpine.js Library'],
                                ['product_id' => 'p4', 'title' => 'Tailwind CSS'],
                                ['product_id' => 'p5', 'title' => 'Livewire Components'],
                            ])
                            ->selectedItems([
                                ['product_id' => 'c1', 'title' => 'Web Development'],
                                ['product_id' => 'c2', 'title' => 'PHP Frameworks'],
                                ['product_id' => 'c3', 'title' => 'Frontend Tools'],
                            ]),
                    ]),

                Section::make('Filament Native MultiSelect Component')
                    ->description('Filament\'s built-in MultiSelect component for comparison')
                    ->schema([
                        CheckboxList::make('filament_checkbox_grouped_tags')
                            ->label('Native CheckboxList - Flattened Options')
                            ->options([
                                '1.1' => 'Group 1 - Family',
                                '1.2' => 'Group 1 - Family in law',
                                '1.3' => 'Group 1 - Co-workers',
                                '2.1' => 'Group 2 - Friends',
                                '2.2' => 'Group 2 - Hockey club',
                                '2.3' => 'Group 2 - Startup Investor Community',
                                '3.1' => 'Group 3 - Swiss Embassy',
                                '3.2' => 'Group 3 - Zurich Meetup Group',
                            ])
                            ->searchable()
                            ->bulkToggleable()
                            ->columns(2)
                            ->descriptions([
                                '1.1' => 'Personal family members',
                                '1.2' => 'Extended family through marriage',
                                '1.3' => 'Professional colleagues',
                                '2.1' => 'Personal friends',
                                '2.2' => 'Sports team members',
                                '2.3' => 'Business network',
                                '3.1' => 'Official diplomatic contacts',
                                '3.2' => 'Local community group',
                            ]),
                    ]),
            ])
            ->statePath('data');
    }
}
