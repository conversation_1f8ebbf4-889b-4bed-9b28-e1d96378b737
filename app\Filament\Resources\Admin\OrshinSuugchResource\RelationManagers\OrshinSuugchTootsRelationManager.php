<?php

namespace App\Filament\Resources\Admin\OrshinSuugchResource\RelationManagers;

use App\Models\Orc;
use App\Models\OrshinSuugchToot;
use App\Models\Toot;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class OrshinSuugchTootsRelationManager extends RelationManager
{
    protected static string $relationship = 'orshin_suugch_toots';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('orc_id')
                    ->label('Орц')
                    ->options(function () {
                        /** @var UserInfoService $userInfoService */
                        $userInfoService = resolve(UserInfoService::class);
                        $sukh = $userInfoService->getAUSukh();
                        if ($sukh) {
                            return Orc::query()
                                ->join('korpuses', 'korpuses.id', '=', 'orcs.korpus_id')
                                ->join('bairs', 'bairs.id', '=', 'korpuses.bair_id')
                                ->where('bairs.sukh_id', $sukh->id)
                                ->select('orcs.*', 'bairs.name as bair_name', 'korpuses.name as korpus_name')
                                ->get()
                                ->mapWithKeys(function ($orc) {
                                    // Add separator based on if korpus_name starts with digit
                                    $separator = preg_match('/^\d/', $orc->korpus_name) ? '-' : '';

                                    return [$orc->id => $orc->bair_name.$separator.$orc->korpus_name.'-'.$orc->number.' орц'];
                                });
                        }

                        return [];
                    })
                    ->live()
                    ->afterStateUpdated(fn (callable $set) => $set('toot_id', null))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('toot_id')
                    ->label('Тоот')
                    ->options(function (Get $get) {
                        $orcId = $get('orc_id');
                        if (! $orcId) {
                            return [];
                        }

                        return Toot::query()
                            ->where('orc_id', $orcId)
                            ->pluck('number', 'id');
                    })
                    ->required()
                    ->searchable(),

                Forms\Components\TextInput::make(OrshinSuugchToot::STATE_BANK_CODE)
                    ->label('Төрийн банкны код')
                    ->placeholder('****************')
                    ->length(16),

                Forms\Components\TextInput::make('pin')
                    ->label('CV Security Pin')
                    ->hidden(fn (string $operation): bool => $operation === 'create')
                    ->readOnly()
                    ->maxLength(255),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Тоот')
            ->columns([
                Tables\Columns\TextColumn::make('toot.number')
                    ->label('Тоот')
                    ->formatStateUsing(function ($state, $record) {
                        $toot = $record->toot;
                        if (! $toot) {
                            return null;
                        }
                        $orc = $toot->orc;
                        if (! $orc) {
                            return null;
                        }
                        $korpus = $orc->korpus;
                        if (! $korpus) {
                            return null;
                        }
                        $bair = $korpus->bair;
                        if (! $bair) {
                            return null;
                        }
                        $separator = preg_match('/^\d/', $korpus->name) ? '-' : '';

                        // Compose: {bair}{sep}{korpus}-{orc}-{toot}
                        return $bair->name.$separator.$korpus->name.'-'.$orc->number.'-'.$toot->number;
                    }),
                Tables\Columns\TextColumn::make('pin')->label('CV Security Pin'),
                Tables\Columns\TextColumn::make(OrshinSuugchToot::STATE_BANK_CODE)->label('Төрийн банкны код'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }
}
