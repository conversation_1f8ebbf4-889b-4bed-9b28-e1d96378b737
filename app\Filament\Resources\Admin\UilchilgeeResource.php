<?php

namespace App\Filament\Resources\Admin;

use App\Enums\UilchilgeeTypeEnum;
use App\Filament\Resources\Admin\UilchilgeeResource\Pages;
use App\Models\Uilchilgee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class UilchilgeeResource extends Resource
{
    protected static ?string $model = Uilchilgee::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Үйлчилгээ';

    protected static ?string $modelLabel = 'Үйлчилгээ';

    protected static ?int $navigationSort = 3;

    protected static ?string $slug = 'uilchilgees';

    protected static ?string $navigationGroup = 'Биллинг';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(Uilchilgee::NAME)
                            ->label('Нэр'),
                        Forms\Components\Select::make(Uilchilgee::TYPE)
                            ->label('Төрөл')
                            ->options(UilchilgeeTypeEnum::class)
                            ->required(),
                        Forms\Components\TextInput::make(Uilchilgee::PRICE)
                            ->label('Үнэ'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?Uilchilgee $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Uilchilgee $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Uilchilgee $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Uilchilgee $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('type')->label('Төрөл')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('price')->label('Үнэ')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUilchilgees::route('/'),
            'create' => Pages\CreateUilchilgee::route('/create'),
            'edit' => Pages\EditUilchilgee::route('/{record}/edit'),
        ];
    }
}
