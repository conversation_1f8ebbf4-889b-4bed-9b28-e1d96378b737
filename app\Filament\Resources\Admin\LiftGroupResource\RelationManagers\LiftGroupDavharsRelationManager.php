<?php

namespace App\Filament\Resources\Admin\LiftGroupResource\RelationManagers;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LiftGroupDavharsRelationManager extends RelationManager
{
    protected static string $relationship = 'liftGroupDeviceDavhars';

    protected static ?string $title = 'Давхрууд';

    protected static ?string $modelLabel = 'давхар';

    protected static ?string $pluralModelLabel = 'давхрууд';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('code')
                    ->label('Код')
                    ->maxLength(30)
                    ->helperText('CVSecurity-д хэрэглэгдэх код'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('davhar.name')
            ->columns([
                TextColumn::make('davhar.name')
                    ->label('Давхрын нэр')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('davhar.number')
                    ->label('Давхрын дугаар')
                    ->sortable(),

                TextColumn::make('code')
                    ->label('Код')
                    ->searchable()
                    ->placeholder('Тохируулагдаагүй'),

                TextColumn::make('created_at')
                    ->label('Үүсгэсэн')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Засагдсан')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->hidden(), // Hide create action since davhars are auto-generated
            ])
            ->actions([
                EditAction::make()
                    ->label('Засах'),
                DeleteAction::make()
                    ->label('Устгах'),
            ])
            ->bulkActions([
                //
            ])
            ->modifyQueryUsing(fn ($query) => $query->join('davhars', 'lift_group_device_davhars.davhar_id', '=', 'davhars.id'))
            ->defaultSort('davhars.number', 'asc');
    }
}
