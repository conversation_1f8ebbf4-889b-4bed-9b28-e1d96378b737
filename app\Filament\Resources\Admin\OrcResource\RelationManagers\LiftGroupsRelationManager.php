<?php

namespace App\Filament\Resources\Admin\OrcResource\RelationManagers;

use App\Filament\Resources\Admin\LiftGroupResource;
use App\Models\LiftGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class LiftGroupsRelationManager extends RelationManager
{
    protected static string $relationship = 'liftGroups';

    protected static ?string $modelLabel = 'лифт групп';

    protected static ?string $pluralModelLabel = 'Лифтийн группүүд';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Нэр')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')->label('Нэр'),
                Tables\Columns\TextColumn::make('devices_count')->label('Төхөөрөмжийн тоо')
                    ->counts('devices'),
                Tables\Columns\TextColumn::make('created_at')->label('Үүсгэсэн')->dateTime(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->url(function (): string {
                        $orcId = $this->getOwnerRecord()->id;
                        $url = LiftGroupResource::getUrl('create').'?orc_id='.$orcId;
                        \Log::info('RelationManager creating URL', [
                            'orc_id' => $orcId,
                            'url' => $url,
                        ]);

                        return $url;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn (LiftGroup $record): string => LiftGroupResource::getUrl('edit', ['record' => $record])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
