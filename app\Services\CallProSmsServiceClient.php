<?php

namespace App\Services;

use App\Interfaces\CallProSmsInterface;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class CallProSmsServiceClient implements CallProSmsInterface
{
    /**
     * {@inheritDoc}
     *
     * @throws Exception|GuzzleException
     */
    public function sendSms($phone, $text): bool
    {
        try {
            $callProHost = config('services.callpro.host');
            $callProKey = config('services.callpro.key');
            $callProPhoneNumber = config('services.callpro.phone_number');
            $client = new Client;
            $r = $client->get("$callProHost?key=".$callProKey.'&from='.$callProPhoneNumber.'&to='.$phone.'&text='.$text);
            if ($r->getStatusCode() == 200) {
                $result = json_decode($r->getBody()->getContents(), true);
                if (isset($result[0])) {
                    if (count($result[0]) > 0) {
                        if ($result[0]['Result'] == 'SUCCESS') {
                            return true;
                        } else {
                            Log::warning('callpro: phone - '.$phone);
                            Log::warning($result);

                            return false;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            Log::warning('CallPro Message '.$e->getMessage());
            throw $e;
        }

        return false;
    }
}
