<?php

namespace App\Filament\Resources\SuperAdmin\ContractResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractResource;
use App\Models\Contract;
use App\Services\StorageService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContract extends EditRecord
{
    protected static string $resource = ContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->after(function (): void {
                    $service = resolve(StorageService::class);
                    $service->deleteFileFromMinio($this->record);
                    $contractTemplate = Contract::find($this->record->id);
                    $contractTemplate->delete();
                }),
        ];
    }

    protected function afterSave(): void
    {
        $service = resolve(StorageService::class);
        $service->deleteFileFromMinio($this->record);
        $service->createContractPDF($this->record);
    }
}
