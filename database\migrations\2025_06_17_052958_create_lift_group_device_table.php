<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lift_group_devices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lift_group_id')->constrained('lift_groups')->restrictOnDelete();
            $table->string('device_id')->unique(); // Each device can only belong to one lift group
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lift_group_devices');
    }
};
