<?php

namespace App\Filament\Resources\SuperAdmin\SBUserResource\Pages;

use App\Filament\Resources\SuperAdmin\SBUserResource;
use App\Models\SBUser;
use App\Services\ToolService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;

class CreateSBUser extends CreateRecord
{
    protected static string $resource = SBUserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $pswd = resolve(ToolService::class)->generateCode(24);
        $data[SBUser::PASSWORD] = $pswd;
        Cache::put('password', $pswd, now()->addMinutes(1));

        return $data;
    }
}
