<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChangeStatusOpenDoorRequest;
use App\Http\Requests\CreateLogOpenDoorRequest;
use App\Http\Resources\LogOpenDoor as LogOpenDoorResource;
use App\Models\Constant\ConstData;
use App\Models\LogOpenDoor;
use App\Services\LogOpenDoorService;

class LogOpenDoorController extends Controller
{
    /**
     * Нэвтрэх үед ЛОГ үүсгэх.
     *
     * Оршин суугч хаалгаар нэвтрэх үед Лог үүсгэх зориулалттай.
     */
    public function createLogOpenDoor(CreateLogOpenDoorRequest $request)
    {
        $orshinSuugchId = $request->input(CreateLogOpenDoorRequest::PARAMETER_ORSHIN_SUUGCH_ID);
        $devEui = $request->input(CreateLogOpenDoorRequest::PARAMETER_DEV_EUI);
        $product = $request->input(CreateLogOpenDoorRequest::PARAMETER_PRODUCT);

        $logOpenDoorService = resolve(LogOpenDoorService::class);
        $logOpenDoor = $logOpenDoorService->createLogOpenDoor($orshinSuugchId, $devEui, $product);

        return new LogOpenDoorResource($logOpenDoor);
    }

    /**
     * Нэвтрэх ЛОГ-н төлөв хөтлөх.
     *
     * Нэвтрэх Лог-н төлөвийг өөрчлөх зориулалттай.
     */
    public function changeStatusLogOpenDoor(ChangeStatusOpenDoorRequest $request)
    {
        $logNo = $request->input(ChangeStatusOpenDoorRequest::PARAMETER_LOG_NO);
        $devEui = $request->input(ChangeStatusOpenDoorRequest::PARAMETER_DEV_EUI);
        $status = $request->input(ChangeStatusOpenDoorRequest::PARAMETER_STATUS);

        $logOpenDoor = LogOpenDoor::where([
            LogOpenDoor::LOG_NO => $logNo,
            LogOpenDoor::DEV_EUI => $devEui,
            LogOpenDoor::STATUS => ConstData::LOG_OPEN_DOOR_STATUS_PENDING,
        ])->first();

        if (isset($logOpenDoor)) {
            $logOpenDoor->status = $status;
            $logOpenDoor->save();
        }

        return ConstData::successMSG();
    }
}
