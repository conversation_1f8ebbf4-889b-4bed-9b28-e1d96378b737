<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Davhar
 *
 * @property int $id
 * @property int $korpus_id
 * @property string $name
 * @property int $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Korpus $korpus
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar query()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Davhar extends Model
{
    use HasDeleteProtection;
    use HasFactory;

    const ID = 'id';

    const KORPUS_ID = 'korpus_id';

    const NAME = 'name';

    const NUMBER = 'number';

    const RELATION_KORPUS = 'korpus';

    const RELATION_TOOTS = 'toots';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::NAME,
        self::NUMBER,
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function toots(): HasMany
    {
        return $this->hasMany(Toot::class)->orderBy('number', 'asc');
    }

    public static function getDeleteProtectionMessage(): string
    {
        return 'Cannot delete this floor because it has toots. Please delete all toots first.';
    }
}
