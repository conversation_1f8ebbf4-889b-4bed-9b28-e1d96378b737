<?php

namespace App\Services;

use App\Exceptions\SystemException;
use App\Models\Constant\ConstData;
use App\Models\Invoice;
use App\Models\InvoiceMember;
use App\Models\Package;
use App\Models\QpayToken;
use Illuminate\Support\Facades\Http;

class InvoiceService
{
    public function createQpayInvoice($orshinSuugchId, $korpusId, $number, $packageId, $month, $memberIds)
    {
        $package = Package::find($packageId);
        if ($package->is_free) {
            $erkhService = resolve(ErkhService::class);

            return $erkhService->setFreeErkh($orshinSuugchId, $korpusId, $number, $package->id);
        } else {
            $qpayToken = QpayToken::where(QpayToken::ORSHIN_SUUGCH_ID, $orshinSuugchId)->latest()->first();
            if (isset($qpayToken)) {
                return $this->createQpayInvoiceByToken($qpayToken, $orshinSuugchId, $korpusId, $number, $package, $month, $memberIds);
            } else {
                return $this->createNewInvoice($orshinSuugchId, $korpusId, $number, $package, $month, $memberIds);
            }
        }
    }

    public function createNewInvoice($orshinSuugchId, $korpusId, $number, $package, $month, $memberIds)
    {
        $qpayToken = $this->createQpayToken($orshinSuugchId);

        return $this->createQpayInvoiceByToken($qpayToken, $orshinSuugchId, $korpusId, $number, $package, $month, $memberIds);
    }

    public function createQpayInvoiceByToken($qpayToken, $orshinSuugchId, $korpusId, $number, $package, $month, $memberIds)
    {
        $toolService = resolve(ToolService::class);
        $invoiceNo = $toolService->generateUniqId();
        $invoiceCode = config('services.qpay.invoice_code');
        $appUrl = config('services.main.app_url');
        $invoiceReceiverCode = 'terminal';
        $invoiceDescription = $package->name.' '.$invoiceNo;

        $service = resolve(PackageService::class);
        $totalPrice = $service->getTotalPrice($package->id, $month, (count($memberIds) + 1));

        if (! isset($totalPrice) || $totalPrice['total_price'] < 100) {
            throw new SystemException(ConstData::QPAY_EXCEPTION, 3);
        }

        $totalAmount = $totalPrice['total_price'];
        $invoiceData = [
            'invoice_code' => $invoiceCode,
            'sender_invoice_no' => $invoiceNo,
            'invoice_receiver_code' => $invoiceReceiverCode,
            'invoice_description' => $invoiceDescription,
            'amount' => $totalAmount,
            'callback_url' => "$appUrl/change-invoice-status?os=".$orshinSuugchId.'&no='.$invoiceNo,
        ];

        $qpay_url = config('services.qpay.host');
        $invoice = Http::withoutVerifying()
            ->withHeaders(['Authorization' => 'Bearer '.$qpayToken->access_token, 'Cache-Control' => 'no-cache'])
            ->withOptions(['verify' => false])
            ->post($qpay_url.'/v2/invoice', $invoiceData);

        if ($invoice->status() == 200) {
            $newInvoice = Invoice::create([
                Invoice::ORSHIN_SUUGCH_ID => $orshinSuugchId,
                Invoice::KORPUS_ID => $korpusId,
                Invoice::NUMBER => $number,
                Invoice::PACKAGE_ID => $package->id,
                Invoice::PACKAGE_NAME => $package->name,
                Invoice::PACKAGE_PRODUCTS => $package->products,
                Invoice::INVOICE_CODE => $invoiceCode,
                Invoice::SENDER_INVOICE_NO => $invoiceNo,
                Invoice::INVOICE_RECEIVER_CODE => $invoiceReceiverCode,
                Invoice::INVOICE_DESCRIPTION => $invoiceDescription,
                Invoice::AMOUNT => $totalAmount,
                Invoice::STATUS => ConstData::INVOICE_STATUS_PENDING,
                Invoice::INVOICE_ID => $invoice['invoice_id'],
                Invoice::VALID_DAY => $month * 30,
            ]);
            $newInvoice->qr_text = $invoice['qr_text'];
            $newInvoice->qr_image = $invoice['qr_image'];
            $newInvoice->qPay_shortUrl = $invoice['qPay_shortUrl'];
            $newInvoice->urls = $invoice['urls'];

            $invoiceMembers = [];
            foreach ($memberIds as $key => $id) {
                $invoiceMembers[] = new InvoiceMember([InvoiceMember::ORSHIN_SUUGCH_ID => $id]);
            }
            $newInvoice->invoice_members()->saveMany($invoiceMembers);

            return $newInvoice;
        } elseif ($invoice->status() == 401) {
            $this->deleteQpayToken($orshinSuugchId);

            return $this->createNewInvoice($orshinSuugchId, $korpusId, $number, $package, $month, $memberIds);
        }
    }

    public function createQpayToken($orshinSuugchId)
    {
        $qpayHost = config('services.qpay.host');
        $username = config('services.qpay.username');
        $password = config('services.qpay.password');
        $token = Http::withBasicAuth($username, $password)->post($qpayHost.'/v2/auth/token');
        if ($token->successful()) {
            $qpayToken = QpayToken::create([
                QpayToken::ORSHIN_SUUGCH_ID => $orshinSuugchId,
                QpayToken::TOKEN_TYPE => $token['token_type'],
                QpayToken::REFRESH_EXPIRES_IN => $token['refresh_expires_in'],
                QpayToken::REFRESH_TOKEN => $token['refresh_token'],
                QpayToken::ACCESS_TOKEN => $token['access_token'],
                QpayToken::EXPIRES_IN => $token['expires_in'],
                QpayToken::SCOPE => $token['scope'],
                QpayToken::SESSION_STATE => $token['session_state'],
            ]);
        }
        if (! isset($qpayToken)) {
            throw new SystemException(ConstData::QPAY_EXCEPTION, 1);
        }

        return $qpayToken;
    }

    // invoiceId - qpay-n invoice_id shu
    public function isPaidInvoice($orshinSuugchId, $invoiceId)
    {
        $qpay_url = config('services.qpay.host');
        $qpayToken = QpayToken::where(QpayToken::ORSHIN_SUUGCH_ID, $orshinSuugchId)->first();
        $accessToken = $qpayToken->access_token;
        $invoiceData = [
            'object_type' => 'INVOICE',
            'object_id' => $invoiceId,
        ];

        $response = Http::withoutVerifying()
            ->withHeaders(['Authorization' => 'Bearer '.$accessToken, 'Cache-Control' => 'no-cache'])
            ->withOptions(['verify' => false])
            ->post($qpay_url.'/v2/payment/check', $invoiceData);

        return $response['count'] > 0 && $response['rows'][0]['payment_status'] == 'PAID';
    }

    public function changeInvoiceStatus($orshinSuugchId, $invoiceNo)
    {
        $invoice = Invoice::where(Invoice::ORSHIN_SUUGCH_ID, $orshinSuugchId)->where(Invoice::SENDER_INVOICE_NO, $invoiceNo)->first();
        if (isset($invoice)) {
            // $isPaid = $this->isPaidInvoice($orshinSuugchId, $invoice->invoice_id);
            // if (!$isPaid)
            //     return;
            $invoice->status = ConstData::INVOICE_STATUS_COMPLETED;
            $invoice->save();
        }

        return $invoice;
    }

    public function checkQpayInvoiceStatus($id, $orshinSuugchId, $invoiceId, $invoiceNo)
    {
        $invoice = Invoice::where(Invoice::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(Invoice::ID, $invoiceId)
            ->where(Invoice::SENDER_INVOICE_NO, $invoiceNo)->first();
        if (! isset($invoice)) {
            throw new SystemException(ConstData::QPAY_EXCEPTION, 2);
        }

        return $invoice;
    }

    public function deleteQpayToken($orshinSuugchId)
    {
        QpayToken::where(QpayToken::ORSHIN_SUUGCH_ID, $orshinSuugchId)->delete();
    }

    public function deleteInvoice($orshinSuugchId, $invoiceNo)
    {
        Invoice::where(Invoice::ORSHIN_SUUGCH_ID, $orshinSuugchId)->where(Invoice::SENDER_INVOICE_NO, $invoiceNo)->delete();
    }
}
