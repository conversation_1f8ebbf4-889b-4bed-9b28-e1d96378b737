<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\LogOpenDoorResource\Pages;
use App\Models\LogOpenDoor;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LogOpenDoorResource extends Resource
{
    protected static ?string $model = LogOpenDoor::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Лог';

    protected static ?string $modelLabel = 'лог';

    protected static ?int $navigationSort = 6;

    protected static ?string $slug = 'logopendoor';

    protected static ?string $navigationGroup = 'Бусад';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('orshin_suugch.name')->label('Оршин суугчийн нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('dev_eui')->label('Dev_eui')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('log_no')->label('log no')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('product')->label('Бүтээгдэхүүн')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('status')->label('Төлөв')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'completed' => 'success',
                        'canceled' => 'danger',
                    })->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([

            ])
            ->bulkActions([

            ])
            ->emptyStateActions([

            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLogOpenDoors::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderByDesc('created_at');
    }
}
