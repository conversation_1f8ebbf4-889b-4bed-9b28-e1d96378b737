# CVSecurity Integration Documentation

## Overview

This document describes the comprehensive integration between the Smart Door IoT API and the CVSecurity biometric access control system. The integration automatically synchronizes CRUD operations across multiple entities (Sukhs, Bairs, Korpuses, Orcs, and OrshinSuugchToots) with the external CVSecurity system.

## Architecture

### Dual Service Pattern

The integration uses two CVSecurity services:

1. **CvSecurityService** (`app/Services/CvSecurityService/CvSecurityService.php`)
   - Main TCP-based service for person management
   - Used for OrshinSuugchToot synchronization
   - Handles biometric person data

2. **CvSecurityServiceExt** (`app/Services/CvSecurityService/CvSecurityServiceExt.php`)
   - HTTP-based extended service for hierarchical data
   - Used for Sukh, Bair, Korpus, and Orc synchronization
   - Handles organizational structure

### Integration Components

Each entity has its own set of integration components:

- **Observer Classes**: Listen to model events and trigger sync operations
- **Sync Service Classes**: Handle the actual synchronization logic
- **Database Migrations**: Add `code`/`pin` fields to store CVSecurity identifiers

## Entity Integrations

### 1. <PERSON><PERSON> (Residents' Committee) Integration

**Components:**
- `SukhObserver` - Listens to model events
- `SukhSyncService` - Handles synchronization
- Database field: `code` (string, nullable)

**Features:**
- ✅ Automatic CRUD synchronization
- ✅ Code storage and retrieval
- ✅ Service availability checking
- ✅ Comprehensive logging

**API Endpoints:**
- `POST /sukhs/` - Create sukh
- `PUT /sukhs/{code}` - Update sukh
- `DELETE /sukhs/{code}` - Delete sukh

### 2. Bair (Building) Integration

**Components:**
- `BairObserver` - Listens to model events
- `BairSyncService` - Handles synchronization
- Database field: `code` (string, nullable)

**Features:**
- ✅ Automatic CRUD synchronization
- ✅ Cascading updates to related Korpus buildings
- ✅ Building name propagation

**Special Behavior:**
When a Bair name is updated, all related Korpus buildings in CVSecurity are automatically updated with new building names.

### 3. Korpus (Building Block) Integration

**Components:**
- `KorpusObserver` - Listens to model events  
- `KorpusSyncService` - Handles synchronization
- Database field: `code` (string, nullable)

**Features:**
- ✅ Automatic CRUD synchronization
- ✅ Smart building name construction
- ✅ Parent Sukh association

**Building Name Logic:**
```
{bair_name}{separator}{korpus_name}
```
Where separator is:
- `-` (dash) if korpus name starts with a digit
- `` (empty) if korpus name starts with a letter

**Examples:**
- "Building 57" + "1st Block" → "Building 57-1st Block"
- "Building 57" + "Block A" → "Building 57Block A"

### 4. Orc (Entrance) Integration

**Components:**
- `OrcObserver` - Listens to model events
- `OrcSyncService` - Handles synchronization  
- Database field: `code` (string, nullable)

**Features:**
- ✅ Automatic CRUD synchronization
- ✅ Entrance name format: "Орц {number}"
- ✅ Parent Korpus association

**API Endpoints:**
- `POST /entrances/` - Create entrance
- `PUT /entrances/{code}` - Update entrance
- `DELETE /entrances/{code}` - Delete entrance

### 5. OrshinSuugchToot (Person Assignment) Integration

**Components:**
- `OrshinSuugchTootObserver` - Listens to model events
- `OrshinSuugchTootSyncService` - Handles synchronization
- Database field: `pin` (string, nullable)

**Features:**
- ✅ Automatic person data synchronization
- ✅ Biometric integration
- ✅ Contact information mapping

**Data Mapping:**
| Local Field | CVSecurity Field | Notes |
|--------------|------------------|-------|
| `pin` | `pin` | Unique identifier |
| `orshin_suugch.name` | `name` | First name |
| `orshin_suugch.last_name` | `lastName` | Last name |
| `orshin_suugch.phone` | `mobilePhone` | With toot_id prefix |
| `orshin_suugch.email` | `email` | With toot_id prefix |
| `toot.orc.code` | `deptCode` | Department code |

## Configuration

### Environment Variables

```env
# Extended HTTP Service
CV_SECURITY_EXT_HOST=127.0.0.1
CV_SECURITY_EXT_PORT=8001
CV_SECURITY_API_KEY=1234567890abcdef

# Main TCP Service  
CV_SECURITY_HOST=*************
CV_SECURITY_PORT=8098
```

### Service Registration

Register all services in `app/Providers/AppServiceProvider.php`:

```php
// CVSecurity Services
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityService::class);
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

// Sync Services
$this->app->singleton(\App\Services\SukhSyncService::class);
$this->app->singleton(\App\Services\BairSyncService::class);
$this->app->singleton(\App\Services\KorpusSyncService::class);
$this->app->singleton(\App\Services\OrcSyncService::class);
$this->app->singleton(\App\Services\OrshinSuugchTootSyncService::class);
```

### Observer Registration

Register all observers in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
Sukh::observe(SukhObserver::class);
Bair::observe(BairObserver::class);
Korpus::observe(KorpusObserver::class);
Orc::observe(OrcObserver::class);
OrshinSuugchToot::observe(OrshinSuugchTootObserver::class);
```

## Usage Patterns

### Automatic Synchronization

All integrations work automatically when performing CRUD operations:

```php
// Create operations automatically sync
$sukh = Sukh::create([...]);
echo $sukh->code; // e.g., "SUKH_0001"

$korpus = Korpus::create([...]);
echo $korpus->code; // e.g., "BLDG_00001"

$orc = Orc::create([...]);
echo $orc->code; // e.g., "ENT_00001"

$person = OrshinSuugchToot::create([...]);
echo $person->pin; // e.g., "12345"

// Updates and deletes also sync automatically
$sukh->update(['name' => 'Updated Name']);
$sukh->delete();
```

### Manual Synchronization

You can also manually trigger synchronization:

```php
// Get sync services
$sukhSync = app(SukhSyncService::class);
$korpusSync = app(KorpusSyncService::class);
$orcSync = app(OrcSyncService::class);
$personSync = app(OrshinSuugchTootSyncService::class);

// Manual sync operations
$sukhSync->syncCreate($sukh);
$korpusSync->syncUpdate($korpus);
$orcSync->syncDelete($orc);
$personSync->syncCreate($person);
```

## Error Handling

All integrations follow consistent error handling patterns:

### Service Unavailable
- Code/pin fields set to `null`
- Warning logged
- Local operations continue normally

### API Errors
- Code/pin fields set to `null`
- Error logged with response details
- Local operations continue normally

### Network Issues
- Code/pin fields set to `null`
- Exception logged with stack trace
- Local operations continue normally

### Infinite Loop Prevention
- Observers check for specific field changes
- Pin/code-only updates are skipped
- Prevents recursive sync operations

## Testing

### Test Commands

Each integration provides test commands:

```bash
# Sukh integration
php artisan test:sukh-cv-security status
php artisan test:sukh-cv-security create
php artisan test:sukh-cv-security update
php artisan test:sukh-cv-security delete

# Korpus integration
php artisan test:korpus-cv-security status
php artisan test:korpus-cv-security create
php artisan test:korpus-cv-security update
php artisan test:korpus-cv-security delete
php artisan test:korpus-cv-security naming

# Bair integration
php artisan test:bair-cv-security status
php artisan test:bair-cv-security update
php artisan test:bair-cv-security sync-all

# Orc integration
php artisan test:orc-cv-security status
php artisan test:orc-cv-security create
php artisan test:orc-cv-security update
php artisan test:orc-cv-security delete
```

### Test Suites

Comprehensive test suites are available:

```bash
# Run all integration tests
php artisan test tests/Feature/SukhCvSecurityIntegrationTest.php
php artisan test tests/Feature/KorpusCvSecurityIntegrationTest.php
php artisan test tests/Feature/OrcCvSecurityIntegrationTest.php
php artisan test tests/Feature/OrshinSuugchTootCvSecurityIntegrationTest.php
```

## Logging

All integrations provide comprehensive logging:

### Log Levels
- **INFO**: Successful operations
- **WARNING**: Service unavailable, non-critical issues
- **ERROR**: API failures, exceptions
- **DEBUG**: Detailed troubleshooting information

### Log Context
All log entries include:
- Entity ID and relevant identifiers
- CVSecurity codes/pins
- API response details (for errors)
- Full stack traces (for exceptions)

### Example Log Entries

```
[INFO] SukhObserver: Sukh created event triggered {"sukh_id":1,"sukh_name":"Test Sukh"}
[INFO] Sukh successfully synced with CVSecurity on creation {"sukh_id":1,"cv_code":"SUKH_0001"}
[WARNING] CVSecurity service is not available during Korpus creation {"korpus_id":2}
[ERROR] CVSecurity create operation failed for Orc {"orc_id":3,"error":"API Error"}
```

## Troubleshooting

### Common Issues

1. **Code/Pin fields not populated**
   - Check service availability using test commands
   - Verify environment variables
   - Check logs for sync failures

2. **Observers not triggering**
   - Ensure observers are registered in AppServiceProvider
   - Clear application cache: `php artisan cache:clear`
   - Restart application server

3. **Parent relationship errors**
   - Verify parent entities have valid codes
   - Check hierarchical relationships (Sukh → Bair → Korpus → Orc)
   - Ensure proper sync order

4. **API authentication errors**
   - Verify API keys are correct
   - Check CVSecurity service logs
   - Test network connectivity

### Debug Commands

```bash
# Check service status
php artisan tinker
>>> app(\App\Services\CvSecurityService\CvSecurityService::class)->isServiceAvailable()
>>> app(\App\Services\CvSecurityService\CvSecurityServiceExt::class)->isServiceAvailable()

# View configuration
>>> config('services.cv_security')
>>> config('services.cv_security_ext')

# Check recent logs
Get-Content storage/logs/laravel.log -Tail 50

# Clear caches
php artisan cache:clear
php artisan config:clear
```

## Database Schema Changes

### Migration Files

The following migrations add CVSecurity integration fields:

```sql
-- Sukhs table
ALTER TABLE sukhs ADD COLUMN code VARCHAR(255) NULL AFTER bag_id;

-- Bairs table  
ALTER TABLE bairs ADD COLUMN code VARCHAR(255) NULL AFTER sukh_id;

-- Korpuses table
ALTER TABLE korpuses ADD COLUMN code VARCHAR(255) NULL AFTER end_toot_number;

-- Orcs table
ALTER TABLE orcs ADD COLUMN code VARCHAR(255) NULL AFTER end_toot_number;

-- OrshinSuugchToots table (pin field already exists)
-- No migration needed for pin field
```

## Admin Panel Integration

All code/pin fields are integrated into Filament admin panels:

### Form Fields
- **Type**: TextInput (disabled)
- **Labels**: Localized (e.g., "CV Security код")
- **Helper Text**: Explanatory text about automatic population

### Table Columns
- **Type**: Badge columns
- **Colors**: Gray for empty, Success for assigned
- **Format**: Shows "Байхгүй" when empty, actual code/pin when assigned

## Best Practices

1. **Service Availability**: Always check service availability before critical operations
2. **Monitoring**: Monitor logs regularly for sync failures
3. **Graceful Handling**: Handle null codes/pins gracefully in application logic
4. **Testing**: Test thoroughly after CVSecurity service updates
5. **Security**: Keep API keys secure and rotate them regularly
6. **Order**: Ensure proper sync order for hierarchical data (Sukh → Bair → Korpus → Orc)
7. **Dependencies**: Don't create child entities without valid parent codes

## Future Enhancements

Potential improvements for the integration:

1. **Retry Mechanism**: Implement automatic retries for failed sync operations
2. **Queue Integration**: Use Laravel queues for async processing
3. **Bulk Operations**: Optimize for bulk entity operations
4. **Webhook Support**: Listen for CVSecurity webhooks for bidirectional sync
5. **Conflict Resolution**: Handle conflicts when data differs between systems
6. **Sync Status Tracking**: Track sync status and last sync time for each entity
7. **Admin Dashboard**: Create admin dashboard for monitoring sync health
8. **Batch Synchronization**: Support for bulk synchronization operations
