<?php

namespace App\Models;

use App\Enums\NehemjlehStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Nehemjleh
 *
 * @property int $id
 * @property int $year
 * @property int $month
 * @property int $bair_id
 * @property string $bair_name
 * @property string $toot_number
 * @property string|null $orshin_suugch_ovog
 * @property string $orshin_suugch_ner
 * @property string $utas
 * @property string $niit_dun
 * @property string $tulsun_dun
 * @property string $uldegdel_dun
 * @property NehemjlehStatusEnum $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $code
 * @property int|null $orshin_suugch_id
 * @property int|null $sukh_id
 * @property string|null $sukh_name
 * @property string|null $orshin_suugch_uniq_code
 * @property string $niit_nuatgui_dun
 * @property string $nuat_dun
 * @property int|null $aimag_id
 * @property string|null $aimag_name
 * @property int|null $soum_id
 * @property string|null $soum_name
 * @property int|null $bag_id
 * @property string|null $bag_name
 * @property string|null $sukh_registration_number
 * @property int $korpus_id
 * @property string $korpus_name
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh query()
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereAimagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereAimagName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereBagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereBagName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereBairName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereKorpusName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereNiitDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereNiitNuatguiDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereNuatDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereOrshinSuugchNer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereOrshinSuugchOvog($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereOrshinSuugchUniqCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereSoumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereSoumName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereSukhId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereSukhName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereSukhRegistrationNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereTulsunDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereUldegdelDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereUtas($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nehemjleh whereYear($value)
 *
 * @mixin \Eloquent
 */
class Nehemjleh extends Model
{
    use HasFactory;

    const ID = 'id';

    const YEAR = 'year';

    const MONTH = 'month';

    const AIMAG_ID = 'aimag_id';

    const AIMAG_NAME = 'aimag_name';

    const SOUM_ID = 'soum_id';

    const SOUM_NAME = 'soum_name';

    const BAG_ID = 'bag_id';

    const BAG_NAME = 'bag_name';

    const SUKH_ID = 'sukh_id';

    const SUKH_NAME = 'sukh_name';

    const SUKH_REGISTRATION_NUMBER = 'sukh_registration_number';

    const BAIR_ID = 'bair_id';

    const BAIR_NAME = 'bair_name';

    const KORPUS_ID = 'korpus_id';

    const KORPUS_NAME = 'korpus_name';

    const TOOT_NUMBER = 'toot_number';

    const ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    const ORSHIN_SUUGCH_OVOG = 'orshin_suugch_ovog';

    const ORSHIN_SUUGCH_NER = 'orshin_suugch_ner';

    const ORSHIN_SUUGCH_UNIQ_CODE = 'orshin_suugch_uniq_code';

    const UTAS = 'utas';

    const NIIT_DUN = 'niit_dun';

    const TULSUN_DUN = 'tulsun_dun';

    const ULDEGDEL_DUN = 'uldegdel_dun';

    const NIIT_NUATGUI_DUN = 'niit_nuatgui_dun';

    const NUAT_DUN = 'nuat_dun';

    const STATUS = 'status';

    const CODE = 'code';

    protected $casts = [
        'status' => NehemjlehStatusEnum::class,
    ];

    protected $fillable = [
        self::YEAR,
        self::MONTH,
        self::AIMAG_ID,
        self::AIMAG_NAME,
        self::SOUM_ID,
        self::SOUM_NAME,
        self::BAG_ID,
        self::BAG_NAME,
        self::SUKH_ID,
        self::SUKH_NAME,
        self::SUKH_REGISTRATION_NUMBER,
        self::BAIR_ID,
        self::BAIR_NAME,
        self::KORPUS_ID,
        self::KORPUS_NAME,
        self::TOOT_NUMBER,
        self::ORSHIN_SUUGCH_ID,
        self::ORSHIN_SUUGCH_OVOG,
        self::ORSHIN_SUUGCH_NER,
        self::ORSHIN_SUUGCH_UNIQ_CODE,
        self::UTAS,
        self::NIIT_DUN,
        self::TULSUN_DUN,
        self::ULDEGDEL_DUN,
        self::NIIT_NUATGUI_DUN,
        self::NUAT_DUN,
        self::STATUS,
        self::CODE,
    ];
}
