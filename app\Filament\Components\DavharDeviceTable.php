<?php

namespace App\Filament\Components;

use App\Models\Davhar;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Closure;
use Filament\Forms\Components\Field;

class DavharDeviceTable extends Field
{
    protected string $view = 'filament.components.davhar-device-table';

    protected array|Closure $deviceIds = [];
    
    protected $orcId;

    public function deviceIds(array|Closure $deviceIds): static
    {
        $this->deviceIds = $deviceIds;

        return $this;
    }

    public function getDeviceIds(): array
    {
        return $this->evaluate($this->deviceIds) ?? [];
    }
    
    public function setOrcId($orcId): static
    {
        $this->orcId = $orcId;
        return $this;
    }
    
    public function setDeviceIds(array $deviceIds): static
    {
        $this->deviceIds = $deviceIds;
        return $this;
    }

    public function getDavhars(): array
    {
        return Davhar::with('korpus.bair.sukh')
            ->orderBy('korpus_id')
            ->orderBy('number')
            ->get()
            ->map(function ($davhar) {
                return [
                    'id' => $davhar->id,
                    'name' => $davhar->name,
                    'number' => $davhar->number,
                    'display_name' => "{$davhar->name} [{$davhar->number}]",
                ];
            })
            ->toArray();
    }

    public function getDevices(): array
    {
        $deviceIds = $this->getDeviceIds();
        if (empty($deviceIds)) {
            return [];
        }

        $cvSecurityService = app(CvSecurityServiceExt::class);

        // Handle service unavailable case
        if (! $cvSecurityService->isServiceAvailable()) {
            // Return demo devices if service is down
            $devices = [];
            foreach ($deviceIds as $deviceId) {
                $devices[] = [
                    'id' => $deviceId,
                    'name' => "Device {$deviceId} (Demo)",
                    'floors' => [], // Demo floors
                ];
            }

            return $devices;
        }

        $deviceList = $cvSecurityService->getDeviceList() ?: [];

        $devices = [];
        foreach ($deviceList as $device) {
            if (isset($device['id']) && in_array((string) $device['id'], array_map('strval', $deviceIds))) {
                $devices[] = [
                    'id' => $device['id'],
                    'name' => "{$device['dev_alias']} ({$device['device_name']})",
                    'floors' => $device['floors'] ?? [],
                ];
            }
        }

        return $devices;
    }

    public function getFloorMatchingData(): array
    {
        $devices = $this->getDevices();
        $davhars = $this->getDavhars();

        $matchingData = [];

        foreach ($davhars as $davhar) {
            foreach ($devices as $device) {
                $floors = $device['floors'] ?? [];
                $matchingFloors = [];

                // Get all floors for this device to show options
                foreach ($floors as $floor) {
                    if (isset($floor['floor_no'], $floor['name'])) {
                        $matchingFloors[] = [
                            'floor_no' => $floor['floor_no'],
                            'name' => $floor['name'],
                            'id' => $floor['id'] ?? null,
                            'enabled' => $floor['enabled'] ?? false,
                        ];
                    }
                }

                $matchingData[$davhar['id']][$device['id']] = [
                    'all_floors' => $matchingFloors,
                    'davhar_number' => $davhar['number'],
                ];
            }
        }

        return $matchingData;
    }

    /**
     * Static method to check if a davhar-device combination has a valid enabled CVSecurity floor match
     * Correct logic: CVSecurity floor_no + offset = davhar_number
     */
    public static function isValidCombinationStatic($orcId, $davharId, $deviceId, int $offset = 0): bool
    {
        // Create a temporary instance to use existing methods
        $instance = new static('temp');
        $instance->setOrcId($orcId);
        $instance->setDeviceIds([$deviceId]);
        
        return $instance->isValidCombination($davharId, $deviceId, $offset);
    }

    /**
     * Check if a davhar-device combination has a valid enabled CVSecurity floor match
     * Correct logic: CVSecurity floor_no + offset = davhar_number
     */
    public function isValidCombination($davharId, $deviceId, int $offset = 0): bool
    {
        // Convert davharId to integer, but keep deviceId as string for proper comparison
        $davharId = (int) $davharId;
        $deviceId = (string) $deviceId;

        $matchingData = $this->getFloorMatchingData();

        if (! isset($matchingData[$davharId][$deviceId])) {
            return false;
        }

        $data = $matchingData[$davharId][$deviceId];
        $davharNumber = $data['davhar_number'];

        // Find matching floor using correct formula: CVSecurity floor_no + offset = davhar_number
        foreach ($data['all_floors'] as $floor) {
            if ($floor['floor_no'] + $offset === $davharNumber && $floor['enabled']) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get validation status for all davhar-device combinations
     */
    public function getValidationData(): array
    {
        $devices = $this->getDevices();
        $davhars = $this->getDavhars();
        $validationData = [];

        foreach ($davhars as $davhar) {
            foreach ($devices as $device) {
                $defaultOffset = 0; // Default offset
                $isValid = $this->isValidCombination((int) $davhar['id'], (string) $device['id'], $defaultOffset);

                $validationData[$davhar['id']][$device['id']] = [
                    'is_valid' => $isValid,
                    'offset' => $defaultOffset,
                    'expected_cvsecurity_floor_no' => $davhar['number'] - $defaultOffset, // CVSecurity floor needed for this davhar
                ];
            }
        }

        return $validationData;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->default([]);
        $this->live();
    }
}
