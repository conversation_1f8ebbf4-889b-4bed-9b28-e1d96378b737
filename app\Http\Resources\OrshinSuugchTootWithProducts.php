<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrshinSuugchTootWithProducts extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /** @var \App\Models\OrshinSuugchToot $item */
        $item = $this;

        return [
            'id' => $item->id,
            'korpus_id' => $item->korpus_id,
            'number' => $item->number,
            'korpus' => $item->korpus ? new Korpus($item->korpus) : null,
            'products' => $item->products,
        ];
    }
}
