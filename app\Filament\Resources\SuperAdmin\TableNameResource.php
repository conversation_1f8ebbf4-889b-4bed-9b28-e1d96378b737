<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\TableNameResource\Pages;
use App\Filament\Resources\SuperAdmin\TableNameResource\RelationManagers;
use App\Models\TableName;
use App\Services\InfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class TableNameResource extends Resource
{
    protected static ?string $model = TableName::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Модел';

    protected static ?string $modelLabel = 'TableName';

    protected static ?int $navigationSort = 3;

    protected static ?string $slug = 'tablenames';

    protected static ?string $navigationGroup = 'Гэрээ';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('name')
                            ->label('Нэр')
                            ->options(function (callable $get) {
                                $service = resolve(InfoService::class);

                                return $service->getTableNamesInContract();
                            })
                            ->searchable()
                            ->disabled(fn (?TableName $record) => $record != null)
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?TableName $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (TableName $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (TableName $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?TableName $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ColummnNamesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTableNames::route('/'),
            'create' => Pages\CreateTableName::route('/create'),
            'edit' => Pages\EditTableName::route('/{record}/edit'),
        ];
    }
}
