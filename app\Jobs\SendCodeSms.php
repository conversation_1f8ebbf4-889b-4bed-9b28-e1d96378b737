<?php

namespace App\Jobs;

use App\Interfaces\CallProSmsInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendCodeSms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $phone;

    public $text;

    /**
     * Create a new job instance.
     */
    public function __construct($phone, $text)
    {
        $this->phone = $phone;
        $this->text = $text;
    }

    /**
     * Execute the job.
     */
    public function handle(CallProSmsInterface $callProSms): void
    {
        $result = $callProSms->sendSms($this->phone, $this->text);
    }
}
