<?php

namespace App\Services\CvSecurityService;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * CV Security Service Ext
 * This is the existing CV Security service that handles sukhs, korpuses, and orcs synchronization.
 * It uses the cv_security_ext configuration.
 */
class CvSecurityServiceExt
{
    /**
     * Check if CV Security service is available
     * Uses the root endpoint "/" as a health check
     */
    public function isServiceAvailable(): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            if (! $host || ! $port) {
                Log::warning('CV Security EXT service configuration is incomplete');

                return false;
            }

            $url = "http://{$host}:{$port}/";

            // Try root endpoint first (usually doesn't require auth)
            $response = Http::timeout(5)->get($url);

            // If root endpoint fails, try with authentication
            if (! $response->successful() && $apiKey) {
                $response = Http::timeout(5)
                    ->withToken($apiKey)
                    ->get($url);
            }

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('CV Security EXT service health check failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get service status with details
     */
    public function getServiceStatus(): array
    {
        $isAvailable = $this->isServiceAvailable();

        return [
            'service' => 'CV Security EXT',
            'status' => $isAvailable ? 'online' : 'offline',
            'color' => $isAvailable ? 'success' : 'danger',
            'icon' => $isAvailable ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle',
            'host' => config('services.cv_security_ext.host'),
            'port' => config('services.cv_security_ext.port'),
        ];
    }

    /**
     * Create a new Sukh (Residents' Committee)
     */
    public function createSukh(array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for createSukh');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createSukh failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createSukh exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Get Sukh by code
     */
    public function getSukhByCode(string $code): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getSukhByCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getSukhByCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Update Sukh by code
     */
    public function updateSukh(string $code, array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for updateSukh');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateSukh failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateSukh exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Delete Sukh by code
     */
    public function deleteSukh(string $code): bool
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for deleteSukh');

            return false;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteSukh exception: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get next available Sukh code
     */
    public function getNextSukhCode(): ?string
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getNextSukhCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);

                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextSukhCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Create a new Building (Korpus)
     */
    public function createBuilding(array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for createBuilding');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createBuilding failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createBuilding exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Get Building by code
     */
    public function getBuildingByCode(string $code): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getBuildingByCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getBuildingByCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Update Building by code
     */
    public function updateBuilding(string $code, array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for updateBuilding');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateBuilding failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateBuilding exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Delete Building by code
     */
    public function deleteBuilding(string $code): bool
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for deleteBuilding');

            return false;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteBuilding exception: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get next available Building code
     */
    public function getNextBuildingCode(): ?string
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getNextBuildingCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);

                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextBuildingCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Create a new Entrance (Orc)
     */
    public function createEntrance(array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for createEntrance');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createEntrance failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createEntrance exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Get Entrance by code
     */
    public function getEntranceByCode(string $code): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getEntranceByCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getEntranceByCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Update Entrance by code
     */
    public function updateEntrance(string $code, array $data): ?object
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for updateEntrance');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateEntrance failed: '.$response->body());

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateEntrance exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Delete Entrance by code
     */
    public function deleteEntrance(string $code): bool
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for deleteEntrance');

            return false;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteEntrance exception: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get next available Entrance code
     */
    public function getNextEntranceCode(): ?string
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getNextEntranceCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);

                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextEntranceCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Get next available Person PIN
     */
    public function getNextPersonPin(): ?string
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getNextPersonPin');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/persons/next-pin";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);

                return $data['pin'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextPersonPin exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Create EleLevel (Floor) in CVSecurity
     */
    public function createEleLevel(array $data): ?array
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for createEleLevel');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT createEleLevel failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createEleLevel exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Update EleLevel (Floor) in CVSecurity
     */
    public function updateEleLevel(string $code, array $data): ?array
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for updateEleLevel');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT updateEleLevel failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateEleLevel exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Delete EleLevel (Floor) from CVSecurity
     */
    public function deleteEleLevel(string $code): bool
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for deleteEleLevel');

            return false;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            if ($response->successful()) {
                return true;
            }

            Log::error('CV Security EXT deleteEleLevel failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteEleLevel exception: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get EleLevel (Floor) by code from CVSecurity
     */
    public function getEleLevelByCode(string $code): ?array
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for getEleLevelByCode');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT getEleLevelByCode failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getEleLevelByCode exception: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Get Device List from CVSecurity
     *
     * @param  int  $skip  Skip records for pagination
     * @param  int  $limit  Maximum number of records to return
     * @param  string|null  $area_code  Optional area filter
     * @param  bool  $useDemo  Whether to return demo data (used when service unavailable)
     */
    public function getDeviceList(int $skip = 0, int $limit = 100, ?string $area_code = null, bool $useDemo = false): ?array
    {
        if ($useDemo) {
            return $this->getDemoDeviceList();
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/devices/list";

            $query = [
                'skip' => $skip,
                'limit' => $limit,
            ];

            if ($area_code) {
                $query['area_code'] = $area_code;
            }

            $response = Http::withToken($apiKey)
                ->get($url, $query);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT getDeviceList failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return $this->getDemoDeviceList();

        } catch (\Exception $e) {
            Log::error('CV Security EXT getDeviceList exception: '.$e->getMessage());

            return $this->getDemoDeviceList();
        }
    }

    /**
     * Update demo floor states in session
     */
    private function updateDemoFloorStates(array $floorsToUpdate): void
    {
        $sessionKey = 'cv_security_demo_floor_states';
        $currentStates = session($sessionKey, []);

        foreach ($floorsToUpdate as $floor) {
            $currentStates[$floor['id']] = $floor['enabled'];
        }

        session([$sessionKey => $currentStates]);

        Log::info('Updated demo floor states in session', [
            'updated_floors' => count($floorsToUpdate),
            'total_tracked_floors' => count($currentStates),
        ]);
    }

    /**
     * Get demo floor state from session
     */
    private function getDemoFloorState(string $floorId, bool $default = true): bool
    {
        $sessionKey = 'cv_security_demo_floor_states';
        $currentStates = session($sessionKey, []);

        return $currentStates[$floorId] ?? $default;
    }

    /**
     * Get demo device list for when service is unavailable
     */
    private function getDemoDeviceList(): array
    {
        $devices = [];

        // Device 1: 10 floors
        $device1Floors = [];
        for ($i = 1; $i <= 10; $i++) {
            $floorId = '40288149975e5d9401977699a487017'.dechex(29 + $i);
            $device1Floors[] = [
                'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
                'enabled' => $this->getDemoFloorState($floorId, $i % 2 === 1), // Use session state or default
                'floor_no' => $i,
                'floor_no_ex' => (string) $i,
                'name' => "*************(Floor{$i})",
                'active_timeseg_id' => '4028801a96fca2100196fca2605203e0',
                'dev_id' => '40288149975e5d9401977699a4780155',
                'id' => $floorId,
            ];
        }

        $devices[] = [
            'id' => '40288149975e5d9401977699a4780155',
            'dev_alias' => '*************',
            'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
            'device_name' => 'EC10',
            'enabled' => true,
            'gateway' => '***********',
            'ip_address' => '*************',
            'sn' => '6125232240001',
            'subnet_mask' => '*************',
            'floors' => $device1Floors,
        ];

        // Device 2: 16 floors
        $device2Floors = [];
        for ($i = 1; $i <= 16; $i++) {
            $floorId = '40288149975e5d9401977699a487018'.dechex(29 + $i);
            $device2Floors[] = [
                'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
                'enabled' => $this->getDemoFloorState($floorId, $i % 2 === 1), // Use session state or default
                'floor_no' => $i,
                'floor_no_ex' => (string) $i,
                'name' => "*************(Floor{$i})",
                'active_timeseg_id' => '4028801a96fca2100196fca2605203e0',
                'dev_id' => '40288149975e5d9401977699a4780156',
                'id' => $floorId,
            ];
        }

        $devices[] = [
            'id' => '40288149975e5d9401977699a47801566',
            'dev_alias' => '*************',
            'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
            'device_name' => 'EC16',
            'enabled' => true,
            'gateway' => '***********',
            'ip_address' => '*************',
            'sn' => '6125232240002',
            'subnet_mask' => '*************',
            'floors' => $device2Floors,
        ];

        // Device 3: 10 floors
        $device3Floors = [];
        for ($i = 1; $i <= 10; $i++) {
            $floorId = '40288149975e5d9401977699a487019'.dechex(29 + $i);
            $device3Floors[] = [
                'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
                'enabled' => $this->getDemoFloorState($floorId, $i % 2 === 1), // Use session state or default
                'floor_no' => $i,
                'floor_no_ex' => (string) $i,
                'name' => "***********23(Floor{$i})",
                'active_timeseg_id' => '4028801a96fca2100196fca2605203e0',
                'dev_id' => '40288149975e5d9401977699a4780157',
                'id' => $floorId,
            ];
        }

        $devices[] = [
            'id' => '40288149975e5d9401977699a4780157',
            'dev_alias' => '***********23',
            'auth_area_id' => '2b78c24f-b201-4d0a-a7fa-260125068edc',
            'device_name' => 'EC10B',
            'enabled' => true,
            'gateway' => '***********',
            'ip_address' => '***********23',
            'sn' => '6125232240003',
            'subnet_mask' => '*************',
            'floors' => $device3Floors,
        ];

        return $devices;
    }

    /**
     * Update Floor Status Batch - batch update the enabled status of multiple floors
     *
     * @param  array  $floors  Array of floor objects with 'id' and 'enabled' fields
     * @return array|null Response containing updated_floors and failed_updates
     */
    public function updateFloorStatusBatch(array $floors): ?array
    {
        if (! $this->isServiceAvailable()) {
            Log::warning('CV Security EXT service is not available for updateFloorStatusBatch');

            return null;
        }

        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/floors/status";

            $payload = [
                'floors' => $floors,
            ];

            $response = Http::withToken($apiKey)
                ->patch($url, $payload);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT updateFloorStatusBatch failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'payload' => $payload,
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateFloorStatusBatch exception: '.$e->getMessage(), [
                'payload' => $floors,
            ]);

            return null;
        }
    }

    /**
     * Save lift floor changes using batch floor status update
     * Replaces LiftFloorService functionality
     *
     * @param  array  $floorData  Array of device floors with enabled states
     */
    public function saveLiftFloorChanges(\App\Models\LiftGroup $liftGroup, array $floorData): bool
    {
        try {
            $devices = $liftGroup->devices()->get();
            $floorsToUpdate = [];

            foreach ($floorData as $deviceId => $deviceData) {
                if (! $devices->contains('device_id', $deviceId)) {
                    continue;
                }

                $floors = $deviceData['floors'] ?? [];

                foreach ($floors as $floorNo => $floorInfo) {
                    if (isset($floorInfo['id'])) {
                        $floorsToUpdate[] = [
                            'id' => $floorInfo['id'],
                            'enabled' => (bool) ($floorInfo['enabled'] ?? false),
                        ];
                    }
                }
            }

            if (empty($floorsToUpdate)) {
                Log::info('No floors to update for lift group', [
                    'lift_group_id' => $liftGroup->id,
                ]);

                return true;
            }

            // When service is unavailable, simulate successful update for demo data
            if (! $this->isServiceAvailable()) {
                Log::info('CV Security EXT service unavailable, simulating floor status update for demo', [
                    'lift_group_id' => $liftGroup->id,
                    'floors_to_update' => count($floorsToUpdate),
                ]);

                // Store demo floor changes in session for this session
                $this->updateDemoFloorStates($floorsToUpdate);

                return true;
            }

            $result = $this->updateFloorStatusBatch($floorsToUpdate);

            if ($result === null) {
                Log::error('Failed to update floor status batch', [
                    'lift_group_id' => $liftGroup->id,
                    'floors_to_update' => $floorsToUpdate,
                ]);

                return false;
            }

            $updatedCount = count($result['updated_floors'] ?? []);
            $failedCount = count($result['failed_updates'] ?? []);

            Log::info('Lift floor changes saved successfully', [
                'lift_group_id' => $liftGroup->id,
                'updated_floors' => $updatedCount,
                'failed_updates' => $failedCount,
                'failed_floor_ids' => $result['failed_updates'] ?? [],
            ]);

            return $failedCount === 0;

        } catch (\Exception $e) {
            Log::error('Failed to save lift floor changes: '.$e->getMessage(), [
                'lift_group_id' => $liftGroup->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Get existing floor permissions for a lift group
     * Replaces LiftFloorService functionality
     */
    public function getLiftGroupFloorPermissions(\App\Models\LiftGroup $liftGroup): array
    {
        try {
            if (! $this->isServiceAvailable()) {
                Log::warning('CV Security EXT service is not available when trying to get lift group floor permissions');

                return [];
            }

            // Get device list once instead of calling for each device
            $deviceList = $this->getDeviceList();
            if ($deviceList === null) {
                Log::warning('CV Security EXT returned null device list');

                return [];
            }

            $permissions = [];
            $devices = $liftGroup->devices()->get();

            // Create a lookup map for faster device finding
            $deviceMap = collect($deviceList)->keyBy('id');

            foreach ($devices as $device) {
                $deviceData = $deviceMap->get($device->device_id);
                if (! $deviceData) {
                    continue;
                }

                $floors = $deviceData['floors'] ?? [];
                foreach ($floors as $floor) {
                    if (isset($floor['id'])) {
                        $floorKey = "floor_{$floor['id']}";
                        // Get the current enabled state from CVSecurity for this device/floor
                        $permissions[$device->device_id][$floorKey] = $floor['enabled'] ?? false;
                    }
                }
            }

            return $permissions;
        } catch (\Exception $e) {
            Log::error('Failed to get lift group floor permissions: '.$e->getMessage(), [
                'lift_group_id' => $liftGroup->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }
}
