<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\ErkhResource\Pages;
use App\Models\Erkh;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ErkhResource extends Resource
{
    protected static ?string $model = Erkh::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Эрх';

    protected static ?string $modelLabel = 'эрх';

    protected static ?int $navigationSort = 4;

    protected static ?string $slug = 'erkhs';

    protected static ?string $navigationGroup = 'Бусад';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('orshin_suugch.last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orshin_suugch.name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orshin_suugch.phone')->label('Утас')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('number')->label('Тоот')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('begin_date')->label('Багц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('end_date')->label('Бүтээгдэхүүн')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListErkhs::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderByDesc('created_at');
    }
}
