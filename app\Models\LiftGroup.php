<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class LiftGroup extends Model
{
    use HasDeleteProtection, HasFactory;

    protected $fillable = [
        'orc_id',
        'name',
    ];

    public function orc(): BelongsTo
    {
        return $this->belongsTo(Orc::class);
    }

    public function devices(): HasMany
    {
        return $this->hasMany(LiftGroupDevice::class);
    }

    public function liftGroupDeviceDavhars(): HasManyThrough
    {
        return $this->hasManyThrough(
            LiftGroupDeviceDavhar::class,
            LiftGroupDevice::class,
            'lift_group_id',        // Foreign key on LiftGroupDevice table
            'lift_group_device_id', // Foreign key on LiftGroupDeviceDavhar table
            'id',                   // Local key on LiftGroup table
            'id'                    // Local key on LiftGroupDevice table
        );
    }

    public function davhars()
    {
        return Davhar::whereIn('id', function ($query) {
            $query->select('davhar_id')
                ->from('lift_group_device_davhars')
                ->join('lift_group_devices', 'lift_group_devices.id', '=', 'lift_group_device_davhars.lift_group_device_id')
                ->where('lift_group_devices.lift_group_id', $this->id);
        });
    }

    public function allToots()
    {
        // Get all toots through the lift group davhars and their toot relationships
        return Toot::whereIn('id', function ($query) {
            $query->select('toot_id')
                ->from('lift_group_davhar_toots')
                ->join('lift_group_device_davhars', 'lift_group_davhar_toots.lift_group_davhar_id', '=', 'lift_group_device_davhars.id')
                ->join('lift_group_devices', 'lift_group_devices.id', '=', 'lift_group_device_davhars.lift_group_device_id')
                ->where('lift_group_devices.lift_group_id', $this->id);
        });
    }
}
