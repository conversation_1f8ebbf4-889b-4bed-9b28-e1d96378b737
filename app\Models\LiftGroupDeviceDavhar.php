<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LiftGroupDeviceDavhar extends Model
{
    use HasDeleteProtection, HasFactory;

    protected $table = 'lift_group_device_davhars';

    const CODE = 'code';

    const OFFSET = 'offset';

    protected $fillable = [
        'lift_group_device_id',
        'davhar_id',
        'code',
        'offset',
        'enabled',
    ];

    public function liftGroupDevice(): BelongsTo
    {
        return $this->belongsTo(LiftGroupDevice::class, 'lift_group_device_id');
    }

    public function davhar(): BelongsTo
    {
        return $this->belongsTo(Davhar::class);
    }
}
