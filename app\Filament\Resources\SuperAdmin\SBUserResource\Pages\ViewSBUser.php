<?php

namespace App\Filament\Resources\SuperAdmin\SBUserResource\Pages;

use App\Filament\Resources\SuperAdmin\SBUserResource;
use App\Models\SBUser;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Cache;

class ViewSBUser extends ViewRecord
{
    protected static string $resource = SBUserResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data[SBUser::HINT] = Cache::get('password');

        return $data;
    }
}
