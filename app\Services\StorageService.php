<?php

namespace App\Services;

use App\Models\ContractTemplate;
use App\Models\TableName;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpWord\TemplateProcessor;

class StorageService
{
    public function __construct() {}

    public function deleteFileFromMinio($record)
    {
        if (! isset($record) || ! isset($record->file_path)) {
            return;
        }
        Storage::disk('minio')->delete($record->file_path);
        $record->file_path = null;
        $record->save();
    }

    public function putFilesToMinio($tableName, $record, $attachments)
    {
        if (! isset($record)) {
            return;
        }
        $folderName = "$tableName/$record->id/";
        foreach ($attachments as $key => $fileName) {
            if (! str_starts_with($fileName, 'temp/') && Storage::disk('minio')->exists($fileName)) {
                continue;
            }
            $newFileName = str_replace('temp/', $folderName, $fileName);
            Storage::disk('minio')->move("$fileName", $newFileName);
            $attachments[$key] = $newFileName;
            $record->file_path = $newFileName;
            break;
        }
        $record->save();

        return $attachments;
    }

    public function createContractPDF($record)
    {
        $contractTemplate = ContractTemplate::find($record->contract_template_id);
        $docxContents = Storage::disk('minio')->get($contractTemplate->file_path);
        $tempFilePath = tempnam(sys_get_temp_dir(), 'docx_temp');
        file_put_contents($tempFilePath, $docxContents);
        $strContract = 'contracts';

        $templateProcessor = new TemplateProcessor($tempFilePath);
        $tablenames = TableName::with(TableName::RELATION_COLUMN_NAMES)->where(TableName::NAME, 'LIKE', "%$strContract%")->get();
        foreach ($tablenames as $key => $tablename) {
            foreach ($tablename->column_names as $key => $columnName) {
                $value = '';
                if ($tablename->name == $strContract) {
                    if (! isset($record["$columnName->name"])) {
                        continue;
                    }
                    $value = $record["$columnName->name"];
                } else {
                    $subTablenNmes = $tablename->getSubTableNames();
                    $tempData = $record;
                    foreach ($subTablenNmes as $key => $subTablenName) {
                        if ($subTablenName == $strContract) {
                            continue;
                        }
                        $tempData = $tempData[substr($subTablenName, 0, -1)];

                        if ($key == count($subTablenNmes) - 1) {
                            $value = $tempData[$columnName->name];
                        }
                    }
                }
                $templateProcessor->setValue("$columnName->value", $value);
            }
        }
        $outputPath = tempnam(sys_get_temp_dir(), 'docx2_temp');
        $templateProcessor->saveAS($outputPath);

        $minioPath = "contracts/$record->id/contract.docx";
        Storage::disk('minio')->put($minioPath, file_get_contents($outputPath));
        $record->file_path = $minioPath;
        $record->save();
    }
}
