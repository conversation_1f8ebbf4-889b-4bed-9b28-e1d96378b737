<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BpayTransaction extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'invoice_id' => $this->invoiceId,
            'qr_text' => $this->qr_text,
            'qr_image' => $this->qr_image,
            'qPay_shortUrl' => $this->qPay_shortUrl,
            'urls' => BpayUrl::collection($this->urls),
        ];
    }
}
