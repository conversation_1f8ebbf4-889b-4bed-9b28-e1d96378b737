<?php

namespace App\Models;

use App\Enums\ProductEnum;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Package
 *
 * @mixin IdeHelperPackage
 *
 * @property int $id
 * @property string $name
 * @property bool $is_free
 * @property bool $is_limitless
 * @property bool $is_new_os_erkh
 * @property string $price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property AsEnumCollection|null $products
 * @property int $valid_day
 * @property-read mixed $product_names
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackageMember> $package_members
 * @property-read int|null $package_members_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PackageMonth> $package_months
 * @property-read int|null $package_months_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Sukh> $sukhs
 * @property-read int|null $sukhs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Package newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Package newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Package query()
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereIsFree($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereIsLimitless($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereIsNewOsErkh($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Package whereValidDay($value)
 *
 * @mixin \Eloquent
 */
class Package extends Model
{
    use HasFactory;

    const TABLE = 'packages';

    const ID = 'id';

    const NAME = 'name';

    const IS_FREE = 'is_free';

    const IS_LIMITLESS = 'is_limitless';

    const IS_NEW_OS_ERKH = 'is_new_os_erkh';

    const PRICE = 'price';

    const PRODUCTS = 'products';

    const VALID_DAY = 'valid_day';

    const RELATION_SUKHS = 'sukhs';

    const RELATION_PACKAGE_MONTHS = 'package_months';

    const RELATION_PACKAGE_MEMBERS = 'package_members';

    protected $fillable = [
        self::ID,
        self::NAME,
        self::IS_FREE,
        self::IS_LIMITLESS,
        self::IS_NEW_OS_ERKH,
        self::PRICE,
        self::PRODUCTS,
        self::VALID_DAY,
    ];

    protected $casts = [
        self::IS_FREE => 'boolean',
        self::IS_LIMITLESS => 'boolean',
        self::IS_NEW_OS_ERKH => 'boolean',

        'sukhs' => 'array',
        'products' => AsEnumCollection::class.':'.ProductEnum::class,
    ];

    public function sukhs(): BelongsToMany
    {
        return $this->belongsToMany(Sukh::class);
    }

    public function package_months(): HasMany
    {
        return $this->hasMany(PackageMonth::class)->orderBy('value', 'desc');
    }

    public function package_members(): HasMany
    {
        return $this->hasMany(PackageMember::class)->orderBy('value', 'desc');
    }

    public function getProductNamesAttribute()
    {
        if (! isset($this->products)) {
            return '';
        }

        return $this->products->implode('name', '-');
    }
}
