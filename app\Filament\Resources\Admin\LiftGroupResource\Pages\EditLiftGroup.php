<?php

namespace App\Filament\Resources\Admin\LiftGroupResource\Pages;

use App\Filament\Components\DavharDeviceTable;

use App\Filament\Resources\Admin\LiftGroupResource;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Filament\Actions;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditLiftGroup extends EditRecord
{
    protected static string $resource = LiftGroupResource::class;

    public ?int $orcId = null;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->successRedirectUrl(function () {
                    // Redirect to the Orc's lift groups relation after successful deletion
                    if ($this->orcId) {
                        return \App\Filament\Resources\Admin\OrcResource::getUrl('edit', ['record' => $this->orcId]).'?activeRelationManager=1#liftGroups';
                    }

                    // Fallback to the list page
                    return $this->getResource()::getUrl('index');
                }),
        ];
    }

    public function mount(int|string $record): void
    {
        parent::mount($record);

        // Store orc_id from the record for consistency
        $this->orcId = $this->record->orc_id;
    }

    protected function getRedirectUrl(): string
    {
        // If we have an orc_id from the record, redirect back to the Orc's lift groups relation
        if ($this->orcId) {
            return \App\Filament\Resources\Admin\OrcResource::getUrl('edit', ['record' => $this->orcId]).'?activeRelationManager=1#liftGroups';
        }

        // Fallback to the list page
        return $this->getResource()::getUrl('index');
    }

    public function form(\Filament\Forms\Form $form): \Filament\Forms\Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        TextInput::make('name')
                            ->label('Нэр')
                            ->required(),

                    ]),

                Section::make('Төхөөрөмжийн тохиргоо')
                    ->description('Төхөөрөмж сонгож, давхрын зөвшөөрөл тохируулах')
                    ->collapsible()
                    ->collapsed()
                    ->schema([
                        Select::make('device_ids')
                            ->label('Төхөөрөмж сонгох')
                            ->multiple()
                            ->searchable()
                            ->native(false)
                            ->options(function () {
                                $cvSecurityService = app(CvSecurityServiceExt::class);

                                if (! $cvSecurityService->isServiceAvailable()) {
                                    Notification::make()
                                        ->title('CV Security сервис ажиллахгүй байна')
                                        ->body('Демо өгөгдөл ашиглаж байна')
                                        ->warning()
                                        ->persistent()
                                        ->send();
                                }

                                // Use real device data or demo data if service is down
                                $deviceList = $cvSecurityService->getDeviceList();
                                if ($deviceList === null) {
                                    return [];
                                }

                                // Get devices assigned to OTHER lift groups (exclude current group)
                                $currentLiftGroupId = $this->record->id ?? null;
                                $assignedDeviceIds = \App\Models\LiftGroupDevice::when($currentLiftGroupId, function ($query) use ($currentLiftGroupId) {
                                    return $query->where('lift_group_id', '!=', $currentLiftGroupId);
                                })->pluck('device_id')->toArray();

                                // Filter out devices that are assigned to other lift groups
                                $deviceOptions = [];
                                foreach ($deviceList as $device) {
                                    if (! in_array($device['id'], $assignedDeviceIds)) {
                                        $deviceOptions[$device['id']] = "{$device['dev_alias']} ({$device['device_name']})";
                                    }
                                }

                                return $deviceOptions;
                            })
                            ->afterStateUpdated(function ($get, $set) {
                                $deviceIds = $get('device_ids');
                                if (empty($deviceIds)) {
                                    $set('davhar_table_devices', []);

                                    return;
                                }

                                // Get updated device and floor data
                                $cvSecurityService = app(CvSecurityServiceExt::class);

                                if (! $cvSecurityService->isServiceAvailable()) {
                                    Notification::make()
                                        ->title('CV Security сервис ажиллахгүй байна')
                                        ->body('Демо өгөгдөл ашиглаж байна')
                                        ->warning()
                                        ->persistent()
                                        ->send();
                                }

                                $deviceList = $cvSecurityService->getDeviceList() ?: [];

                                // Only process selected devices to avoid duplicates
                                $selectedDeviceIds = array_unique((array) $deviceIds);

                                // Set devices for the davhar table
                                $davharTableDevices = [];
                                foreach ($deviceList as $device) {
                                    if (in_array($device['id'], $selectedDeviceIds)) {
                                        $davharTableDevices[] = [
                                            'id' => $device['id'],
                                            'name' => "{$device['dev_alias']} ({$device['device_name']})",
                                        ];
                                    }
                                }
                                $set('davhar_table_devices', $davharTableDevices);
                            })
                            ->live(),

                        Hidden::make('davhar_table_devices')
                            ->default([])
                            ->live(),

                        Hidden::make('davhar_offsets')
                            ->default([])
                            ->live(),

                        Hidden::make('davhar_floor_nos')
                            ->default([])
                            ->live(),

                        DavharDeviceTable::make('davhar_device_permissions')
                            ->label('Давхрын төхөөрөмжийн зөвшөөрөл')
                            ->deviceIds(function ($get) {
                                return $get('device_ids') ?? [];
                            })
                            ->live()
                            ->visible(function ($get) {
                                $deviceIds = $get('device_ids') ?? [];

                                return ! empty($deviceIds);
                            }),
                    ]),
            ]);
    }

    protected function afterSave(): void
    {
        try {
            $liftGroup = $this->record;
            $cvSecurityService = app(CvSecurityServiceExt::class);

            // Get device IDs from the form
            $deviceIds = $this->data['device_ids'] ?? [];

            // First, remove any devices not in the selected list
            $liftGroup->devices()
                ->whereNotIn('device_id', $deviceIds)
                ->delete();

            // Then create or update the selected devices
            foreach ($deviceIds as $deviceId) {
                $liftGroup->devices()->updateOrCreate(
                    ['device_id' => $deviceId],
                    ['device_id' => $deviceId]
                );
            }

            // Handle davhar-device permissions from the new table - save ALL valid combinations
            $davharDevicePermissions = $this->data['davhar_device_permissions'] ?? [];
            $davharOffsets = $this->data['davhar_offsets'] ?? []; // Get davhar offset data: [davharId][deviceId] = offset
            $davharFloorNos = $this->data['davhar_floor_nos'] ?? []; // Get davhar floor_no data: [davharId][deviceId] = floor_no

            // Get all valid combinations that should be saved (regardless of checkbox state)
            $allValidCombinations = collect();
            
            // First, collect all combinations that have valid floor matches
            foreach ($davharOffsets as $davharId => $deviceOffsets) {
                if (!is_array($deviceOffsets)) continue;
                
                foreach ($deviceOffsets as $deviceId => $offset) {
                    // Check if this combination has a valid CVSecurity floor match
                    if (DavharDeviceTable::isValidCombinationStatic($liftGroup->orc_id, $davharId, $deviceId, (int)$offset)) {
                        $floorNo = isset($davharFloorNos[$davharId][$deviceId]) ? $davharFloorNos[$davharId][$deviceId] : null;

                        $allValidCombinations->push([
                            'davhar_id' => $davharId,
                            'device_id' => $deviceId,
                            'offset' => (int)$offset,
                            'floor_no' => $floorNo,
                            'enabled' => isset($davharDevicePermissions[$davharId][$deviceId]) ?
                                        (bool)$davharDevicePermissions[$davharId][$deviceId] : false,
                        ]);
                    }
                }
            }

            // Remove records that are no longer valid combinations
            $existingRecords = $liftGroup->liftGroupDeviceDavhars()->with('liftGroupDevice')->get();
            foreach ($existingRecords as $existingRecord) {
                $shouldKeep = $allValidCombinations->contains(function ($combination) use ($existingRecord) {
                    return $combination['davhar_id'] == $existingRecord->davhar_id &&
                           $combination['device_id'] == $existingRecord->liftGroupDevice->device_id;
                });

                if (!$shouldKeep) {
                    $existingRecord->delete();
                }
            }

            // Update or create all valid combinations with their enabled state
            foreach ($allValidCombinations as $combination) {
                // Get the lift_group_device record for this device
                $liftGroupDevice = $liftGroup->devices()->where('device_id', $combination['device_id'])->first();

                if ($liftGroupDevice) {
                    // Upsert the LiftGroupDeviceDavhar record with enabled state
                    $liftGroupDeviceDavhar = $liftGroup->liftGroupDeviceDavhars()->updateOrCreate(
                        [
                            'lift_group_device_id' => $liftGroupDevice->id,
                            'davhar_id' => $combination['davhar_id'],
                        ],
                        [
                            'offset' => $combination['offset'],
                            'floor_no' => $combination['floor_no'],
                            'enabled' => $combination['enabled'], // Use actual checkbox state
                        ]
                    );
                }
            }

            

            Notification::make()
                ->title('Давхрын мэдээлэл хадгалагдлаа')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Давхрын мэдээлэл хадгалахад алдаа гарлаа: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Get the current device IDs
        $deviceIds = $this->record->devices()->pluck('device_id')->toArray();
        $data['device_ids'] = $deviceIds;

        $cvSecurityService = app(CvSecurityServiceExt::class);
        $deviceList = $cvSecurityService->getDeviceList() ?: [];

        // Set initial devices for the davhar table
        $davharTableDevices = [];
        foreach ($deviceList as $device) {
            if (in_array($device['id'], $deviceIds)) {
                $davharTableDevices[] = [
                    'id' => $device['id'],
                    'name' => "{$device['dev_alias']} ({$device['device_name']})",
                ];
            }
        }
        $data['davhar_table_devices'] = $davharTableDevices;

        // Get existing davhar-device permissions for the table
        $davharDevicePermissions = [];
        $davharOffsets = [];
        $davharFloorNos = [];
        $existingLiftGroupDeviceDavhars = $this->record->liftGroupDeviceDavhars()->with('liftGroupDevice')->get();

        foreach ($existingLiftGroupDeviceDavhars as $liftGroupDeviceDavhar) {
            $davharId = $liftGroupDeviceDavhar->davhar_id;

            // Get the device_id from the lift_group_device relationship
            $liftGroupDevice = $liftGroupDeviceDavhar->liftGroupDevice;
            if ($liftGroupDevice) {
                $deviceId = $liftGroupDevice->device_id;

                // Set this specific device-davhar combination with its enabled state
                $davharDevicePermissions[$davharId][$deviceId] = $liftGroupDeviceDavhar->enabled ?? true;

                // Store offsets
                if (!isset($davharOffsets[$davharId])) {
                    $davharOffsets[$davharId] = [];
                }
                $davharOffsets[$davharId][$deviceId] = $liftGroupDeviceDavhar->offset ?? 0;

                // Store floor_no values
                if (!isset($davharFloorNos[$davharId])) {
                    $davharFloorNos[$davharId] = [];
                }
                $davharFloorNos[$davharId][$deviceId] = $liftGroupDeviceDavhar->floor_no;
            }
        }

        $data['davhar_device_permissions'] = $davharDevicePermissions;
        $data['davhar_offsets'] = $davharOffsets;
        $data['davhar_floor_nos'] = $davharFloorNos;

        return $data;
    }
}
        
