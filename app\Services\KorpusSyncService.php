<?php

namespace App\Services;

use App\Models\Korpus;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

class KorpusSyncService
{
    protected CvSecurityServiceExt $cvSecurityService;

    public function __construct(CvSecurityServiceExt $cvSecurityService)
    {
        $this->cvSecurityService = $cvSecurityService;
    }

    /**
     * Sync Korpus creation with CVSecurity service
     */
    public function syncCreate(Korpus $korpus): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Korpus creation', [
                    'korpus_id' => $korpus->id,
                    'korpus_name' => $korpus->name,
                ]);
                $this->updateKorpusCode($korpus, null);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareKorpusData($korpus);

            // Call CVSecurity create endpoint
            $response = $this->cvSecurityService->createBuilding($data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateKorpusCode($korpus, $code);
                Log::info('Korpus successfully synced with CVSecurity on creation', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $code,
                ]);
            } else {
                $this->updateKorpusCode($korpus, null);
                Log::error('CVSecurity create operation failed for Korpus', [
                    'korpus_id' => $korpus->id,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            $this->updateKorpusCode($korpus, null);
            Log::error('Exception during Korpus CVSecurity sync on creation', [
                'korpus_id' => $korpus->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync Korpus update with CVSecurity service
     */
    public function syncUpdate(Korpus $korpus): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Korpus update', [
                    'korpus_id' => $korpus->id,
                    'korpus_name' => $korpus->name,
                ]);

                return;
            }

            // If no code exists, treat as create operation
            if (! $korpus->code) {
                $this->syncCreate($korpus);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareKorpusData($korpus);

            // Call CVSecurity update endpoint
            $response = $this->cvSecurityService->updateBuilding($korpus->code, $data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateKorpusCode($korpus, $code);
                Log::info('Korpus successfully synced with CVSecurity on update', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $code,
                ]);
            } else {
                Log::error('CVSecurity update operation failed for Korpus', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $korpus->code,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Korpus CVSecurity sync on update', [
                'korpus_id' => $korpus->id,
                'cv_code' => $korpus->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync Korpus deletion with CVSecurity service
     */
    public function syncDelete(Korpus $korpus): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Korpus deletion', [
                    'korpus_id' => $korpus->id,
                    'korpus_name' => $korpus->name,
                ]);

                return;
            }

            // If no code exists, nothing to delete in CVSecurity
            if (! $korpus->code) {
                Log::info('No CVSecurity code found for Korpus deletion', [
                    'korpus_id' => $korpus->id,
                ]);

                return;
            }

            // Call CVSecurity delete endpoint
            $success = $this->cvSecurityService->deleteBuilding($korpus->code);

            if ($success) {
                Log::info('Korpus successfully deleted from CVSecurity', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $korpus->code,
                ]);
            } else {
                Log::error('CVSecurity delete operation failed for Korpus', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $korpus->code,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Korpus CVSecurity sync on deletion', [
                'korpus_id' => $korpus->id,
                'cv_code' => $korpus->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Prepare Korpus data for CVSecurity API
     * Building name is constructed as "{building_name} {korpus_name}"
     */
    protected function prepareKorpusData(Korpus $korpus): array
    {
        // Load the bair and sukh relationships if not already loaded
        if (! $korpus->relationLoaded('bair')) {
            $korpus->load('bair.sukh');
        } elseif ($korpus->bair && ! $korpus->bair->relationLoaded('sukh')) {
            $korpus->bair->load('sukh');
        }

        $buildingName = $korpus->bair ? $korpus->bair->name : 'Unknown Building';

        // If korpus name starts with digit, concatenate with '-', else with empty string
        $separator = is_numeric(substr($korpus->name, 0, 1)) ? '-' : '';
        $fullBuildingName = $buildingName.$separator.$korpus->name;

        $data = [
            'name' => $fullBuildingName,
        ];

        // Add parent Sukh code if available
        if ($korpus->bair && $korpus->bair->sukh && $korpus->bair->sukh->code) {
            $data['parent_code'] = $korpus->bair->sukh->code;
        }

        return $data;
    }

    /**
     * Extract code from CVSecurity response
     */
    protected function extractCodeFromResponse(?object $response): ?string
    {
        if (! $response) {
            return null;
        }

        // Try to extract code from different possible response structures
        if (isset($response->code)) {
            return $response->code;
        }

        if (isset($response->building->code)) {
            return $response->building->code;
        }

        if (isset($response->area->code)) {
            return $response->area->code;
        }

        if (isset($response->department->code)) {
            return $response->department->code;
        }

        return null;
    }

    /**
     * Update Korpus code field without triggering observers
     */
    protected function updateKorpusCode(Korpus $korpus, ?string $code): void
    {
        // Use updateQuietly to avoid triggering observers again
        $korpus->updateQuietly([Korpus::CODE => $code]);
    }

    /**
     * Sync existing Korpus with CVSecurity (for read operations)
     */
    public function syncRead(Korpus $korpus): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Korpus read sync', [
                    'korpus_id' => $korpus->id,
                ]);

                return;
            }

            // If no code exists, nothing to read from CVSecurity
            if (! $korpus->code) {
                Log::info('No CVSecurity code found for Korpus read sync', [
                    'korpus_id' => $korpus->id,
                ]);

                return;
            }

            // Call CVSecurity read endpoint
            $response = $this->cvSecurityService->getBuildingByCode($korpus->code);

            if ($response) {
                Log::info('Korpus data successfully retrieved from CVSecurity', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $korpus->code,
                ]);
            } else {
                Log::warning('CVSecurity read operation returned no data for Korpus', [
                    'korpus_id' => $korpus->id,
                    'cv_code' => $korpus->code,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Korpus CVSecurity sync on read', [
                'korpus_id' => $korpus->id,
                'cv_code' => $korpus->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
