<?php

namespace App\Http\Controllers;

use App\Http\Requests\GetPackageMonthsRequest;
use App\Http\Resources\PackageMonth as PackageMonthResource;
use App\Models\PackageMonth;

class PackageMonthController extends Controller
{
    /**
     * Бүтээгдэхүүний ба.
     *
     * Тухайн оршин суугчийн хамаарал бүхий орцнуудын мэдээлэлийг авах зориулалттай.
     */
    public function index(GetPackageMonthsRequest $request)
    {
        $packageId = $request->input(GetPackageMonthsRequest::PARAMETER_PACKAGE_ID);
        $limit = $request->input(GetPackageMonthsRequest::LIMIT, 10);
        $packageMonths = PackageMonth::where(PackageMonth::PACKAGE_ID, $packageId);

        $sort = $request->input(GetPackageMonthsRequest::SORT);
        if (isset($sort[GetPackageMonthsRequest::SORT_FIELD])) {
            $sortField = $sort[GetPackageMonthsRequest::SORT_FIELD];
            $sortType = $sort[GetPackageMonthsRequest::SORT_TYPE];
            $packageMonths = $packageMonths->orderBy($sortField, $sortType);
        }

        $packageMonths = $packageMonths->paginate($limit);
        $packageMonths->prepend(new PackageMonth([PackageMonth::VALUE => 1, PackageMonth::DISCOUNT => 0]));

        return PackageMonthResource::collection($packageMonths);
    }
}
