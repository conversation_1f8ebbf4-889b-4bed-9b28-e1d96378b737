<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\LogOpenDoor
 *
 * @mixin IdeHelperLogOpenDoor
 *
 * @property int $id
 * @property int $orshin_suugch_id
 * @property string $dev_eui
 * @property string $log_no
 * @property string $product
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor query()
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereDevEui($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereLogNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereProduct($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogOpenDoor whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class LogOpenDoor extends Model
{
    use HasFactory;

    const ID = 'id';

    const ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    const DEV_EUI = 'dev_eui';

    const LOG_NO = 'log_no';

    const PRODUCT = 'product';

    const STATUS = 'status';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::DEV_EUI,
        self::LOG_NO,
        self::PRODUCT,
        self::STATUS,
    ];

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }
}
