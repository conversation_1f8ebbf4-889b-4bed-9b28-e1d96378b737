<?php

namespace App\Filament\Components;

use Filament\Forms\Components\Field;

class GroupedMultiSelect extends Field
{
    protected string $view = 'filament.components.grouped-multi-select';

    protected array $groups = [];

    protected string $placeholder = 'Choose a tag';

    protected bool $searchable = true;

    public function groups(array $groups): static
    {
        $this->groups = $groups;

        return $this;
    }

    public function getGroups(): array
    {
        return $this->groups;
    }

    public function placeholder(string $placeholder): static
    {
        $this->placeholder = $placeholder;

        return $this;
    }

    public function getPlaceholder(): string
    {
        return $this->placeholder;
    }

    public function searchable(bool $searchable = true): static
    {
        $this->searchable = $searchable;

        return $this;
    }

    public function isSearchable(): bool
    {
        return $this->searchable;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->default([]);
        $this->live();
    }
}
