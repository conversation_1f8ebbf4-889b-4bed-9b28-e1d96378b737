<?php

namespace App\Filament\Resources\Admin\LiftGroupResource\Pages;

use App\Filament\Components\DavharDeviceTable;

use App\Filament\Resources\Admin\LiftGroupResource;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Filament\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateLiftGroup extends CreateRecord
{
    protected static string $resource = LiftGroupResource::class;

    public ?int $orcId = null;

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function getCreateFormAction(): Action
    {
        return Action::make('create')
            ->label(__('filament-panels::resources/pages/create-record.form.actions.create.label'))
            ->submit('create')
            ->keyBindings(['mod+s']);
    }

    public function mount(): void
    {
        parent::mount();

        // Store orc_id from query parameter when component mounts
        $this->orcId = request()->query('orc_id');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($this->orcId) {
            $data['orc_id'] = (int) $this->orcId;
        } else {
            throw new \Exception('orc_id is required but not found');
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        // If we have an orc_id, redirect back to the Orc's lift groups relation
        if ($this->orcId) {
            return \App\Filament\Resources\Admin\OrcResource::getUrl('edit', ['record' => $this->orcId]).'?activeRelationManager=1#liftGroups';
        }

        // Fallback to the list page
        return $this->getResource()::getUrl('index');
    }

    public function form(\Filament\Forms\Form $form): \Filament\Forms\Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        TextInput::make('name')
                            ->label('Нэр')
                            ->required(),

                    ]),

                Section::make('Төхөөрөмжийн тохиргоо')
                    ->description('Төхөөрөмж сонгож, давхрын зөвшөөрөл тохируулах')
                    ->collapsible()
                    ->collapsed()
                    ->schema([
                        Select::make('device_ids')
                            ->label('Төхөөрөмж сонгох')
                            ->multiple()
                            ->searchable()
                            ->native(false)
                            ->options(function () {
                                $cvSecurityService = app(CvSecurityServiceExt::class);

                                if (! $cvSecurityService->isServiceAvailable()) {
                                    Notification::make()
                                        ->title('CV Security сервис ажиллахгүй байна')
                                        ->body('Демо өгөгдөл ашиглаж байна')
                                        ->warning()
                                        ->persistent()
                                        ->send();
                                }

                                // Use real device data or demo data if service is down
                                $deviceList = $cvSecurityService->getDeviceList();
                                if ($deviceList === null) {
                                    return [];
                                }

                                // Get devices that are already assigned to lift groups
                                $assignedDeviceIds = \App\Models\LiftGroupDevice::pluck('device_id')->toArray();

                                // Filter out devices that are already assigned
                                $deviceOptions = [];
                                foreach ($deviceList as $device) {
                                    if (! in_array($device['id'], $assignedDeviceIds)) {
                                        $deviceOptions[$device['id']] = "{$device['dev_alias']} ({$device['device_name']})";
                                    }
                                }

                                return $deviceOptions;
                            })
                            ->afterStateUpdated(function ($get, $set) {
                                $deviceIds = $get('device_ids');
                                if (empty($deviceIds)) {
                                    $set('davhar_table_devices', []);

                                    return;
                                }

                                // Get updated device and floor data
                                $cvSecurityService = app(CvSecurityServiceExt::class);

                                // Prepare device floor permissions
                                if (! $cvSecurityService->isServiceAvailable()) {
                                    Notification::make()
                                        ->title('CV Security сервис ажиллахгүй байна')
                                        ->body('Демо өгөгдөл ашиглаж байна')
                                        ->warning()
                                        ->persistent()
                                        ->send();
                                }

                                $deviceList = $cvSecurityService->getDeviceList() ?: [];

                                // Only process selected devices to avoid duplicates
                                $selectedDeviceIds = array_unique((array) $deviceIds);

                                // Set devices for the davhar table
                                $davharTableDevices = [];
                                foreach ($deviceList as $device) {
                                    if (in_array($device['id'], $selectedDeviceIds)) {
                                        $davharTableDevices[] = [
                                            'id' => $device['id'],
                                            'name' => "{$device['dev_alias']} ({$device['device_name']})",
                                        ];
                                    }
                                }
                                $set('davhar_table_devices', $davharTableDevices);
                            })
                            ->live(),

                        Hidden::make('davhar_table_devices')
                            ->default([])
                            ->live(),

                        Hidden::make('davhar_offsets')
                            ->default([])
                            ->live(),

                        DavharDeviceTable::make('davhar_device_permissions')
                            ->label('Давхрын төхөөрөмжийн зөвшөөрөл')
                            ->deviceIds(function ($get) {
                                return $get('device_ids') ?? [];
                            })
                            ->live()
                            ->visible(function ($get) {
                                $deviceIds = $get('device_ids') ?? [];

                                return ! empty($deviceIds);
                            }),
                    ]),
            ]);
    }

    protected function afterCreate(): void
    {
        

        try {
            $liftGroup = $this->record;
            $cvSecurityService = app(CvSecurityServiceExt::class);

            // Get device IDs from the form
            $deviceIds = $this->data['device_ids'] ?? [];

            // Ensure deviceIds is an array
            if (! is_array($deviceIds)) {
                $deviceIds = [];
            }

            // Create the selected devices
            foreach ($deviceIds as $deviceId) {
                $liftGroup->devices()->create([
                    'device_id' => $deviceId,
                ]);
            }

            // Handle davhar-device permissions from the new table - save ALL valid combinations
            $davharDevicePermissions = $this->data['davhar_device_permissions'] ?? [];
            $davharOffsets = $this->data['davhar_offsets'] ?? []; // Get davhar offset data: [davharId][deviceId] = offset

            // Get all valid combinations that should be saved (regardless of checkbox state)
            $allValidCombinations = [];
            
            // First, collect all combinations that have valid floor matches
            foreach ($davharOffsets as $davharId => $deviceOffsets) {
                if (!is_array($deviceOffsets)) continue;
                
                foreach ($deviceOffsets as $deviceId => $offset) {
                    // Check if this combination has a valid CVSecurity floor match
                    if (DavharDeviceTable::isValidCombinationStatic($this->orcId, $davharId, $deviceId, (int)$offset)) {
                        $allValidCombinations[] = [
                            'davhar_id' => $davharId,
                            'device_id' => $deviceId,
                            'offset' => (int)$offset,
                            'enabled' => isset($davharDevicePermissions[$davharId][$deviceId]) ? 
                                        (bool)$davharDevicePermissions[$davharId][$deviceId] : false,
                        ];
                    }
                }
            }

            // Save all valid combinations with their enabled state
            foreach ($allValidCombinations as $combination) {
                $liftGroupDevice = $liftGroup->devices()->where('device_id', $combination['device_id'])->first();

                if ($liftGroupDevice) {
                    $existingRecord = $liftGroup->liftGroupDeviceDavhars()
                        ->where('lift_group_device_id', $liftGroupDevice->id)
                        ->where('davhar_id', $combination['davhar_id'])
                        ->first();

                    if (!$existingRecord) {
                        $liftGroupDeviceDavhar = $liftGroup->liftGroupDeviceDavhars()->create([
                            'lift_group_device_id' => $liftGroupDevice->id,
                            'davhar_id' => $combination['davhar_id'],
                            'offset' => $combination['offset'],
                            'enabled' => $combination['enabled'], // Use actual checkbox state
                        ]);
                    }
                }
            }

            
            

            

            Notification::make()
                ->title('Давхрын мэдээлэл хадгалагдлаа')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Давхрын мэдээлэл хадгалахад алдаа гарлаа: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    
}
