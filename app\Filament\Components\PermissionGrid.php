<?php

namespace App\Filament\Components;

use Closure;
use Filament\Forms\Components\Field;

class PermissionGrid extends Field
{
    protected string $view = 'filament.components.permission-grid';

    protected array|Closure $permissionGroups = [];

    public function permissionGroups(array|Closure $groups): static
    {
        $this->permissionGroups = $groups;

        return $this;
    }

    public function getPermissionGroups(): array
    {
        return $this->evaluate($this->permissionGroups) ?? [];
    }

    public function getPermissionGroupsStateKey(): string
    {
        // Generate a unique key for this field to make groups reactive
        return 'permissionGroups_'.$this->getName();
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->default([]);
        $this->live();
    }
}
