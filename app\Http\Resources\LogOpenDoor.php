<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class LogOpenDoor extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'orshin_suugch_id' => $this->orshin_suugch_id,
            'dev_eui' => $this->dev_eui,
            'log_no' => $this->log_no,
            'product' => $this->product,
            'status' => $this->status,
        ];
    }
}
