<?php

namespace App\Services;

use App\Models\Orc;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

class OrcSyncService
{
    protected CvSecurityServiceExt $cvSecurityService;

    public function __construct(CvSecurityServiceExt $cvSecurityService)
    {
        $this->cvSecurityService = $cvSecurityService;
    }

    /**
     * Sync Orc creation with CVSecurity service
     */
    public function syncCreate(Orc $orc): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Orc creation', [
                    'orc_id' => $orc->id,
                    'orc_number' => $orc->number,
                ]);
                $this->updateOrcCode($orc, null);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareOrcData($orc);

            // Call CVSecurity create endpoint
            $response = $this->cvSecurityService->createEntrance($data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateOrcCode($orc, $code);
                Log::info('Orc successfully synced with CVSecurity on creation', [
                    'orc_id' => $orc->id,
                    'cv_code' => $code,
                ]);
            } else {
                $this->updateOrcCode($orc, null);
                Log::error('CVSecurity create operation failed for Orc', [
                    'orc_id' => $orc->id,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            $this->updateOrcCode($orc, null);
            Log::error('Exception during Orc CVSecurity sync on creation', [
                'orc_id' => $orc->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync Orc update with CVSecurity service
     */
    public function syncUpdate(Orc $orc): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Orc update', [
                    'orc_id' => $orc->id,
                    'orc_number' => $orc->number,
                ]);

                return;
            }

            // If no code exists, treat as create operation
            if (! $orc->code) {
                $this->syncCreate($orc);

                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareOrcData($orc);

            // Call CVSecurity update endpoint
            $response = $this->cvSecurityService->updateEntrance($orc->code, $data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateOrcCode($orc, $code);
                Log::info('Orc successfully synced with CVSecurity on update', [
                    'orc_id' => $orc->id,
                    'cv_code' => $code,
                ]);
            } else {
                Log::error('CVSecurity update operation failed for Orc', [
                    'orc_id' => $orc->id,
                    'cv_code' => $orc->code,
                    'response' => $response,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Orc CVSecurity sync on update', [
                'orc_id' => $orc->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync Orc deletion with CVSecurity service
     */
    public function syncDelete(Orc $orc): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Orc deletion', [
                    'orc_id' => $orc->id,
                    'orc_number' => $orc->number,
                    'cv_code' => $orc->code,
                ]);

                return;
            }

            // If no code exists, nothing to delete in CVSecurity
            if (! $orc->code) {
                Log::info('No CVSecurity code found for Orc deletion', [
                    'orc_id' => $orc->id,
                    'orc_number' => $orc->number,
                ]);

                return;
            }

            // Call CVSecurity delete endpoint
            $success = $this->cvSecurityService->deleteEntrance($orc->code);

            if ($success) {
                Log::info('Orc successfully deleted from CVSecurity', [
                    'orc_id' => $orc->id,
                    'cv_code' => $orc->code,
                ]);
            } else {
                Log::error('CVSecurity delete operation failed for Orc', [
                    'orc_id' => $orc->id,
                    'cv_code' => $orc->code,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Orc CVSecurity sync on deletion', [
                'orc_id' => $orc->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Sync Orc read with CVSecurity service (optional)
     */
    public function syncRead(Orc $orc): void
    {
        try {
            // Check if CVSecurity service is available
            if (! $this->cvSecurityService->isServiceAvailable()) {
                return;
            }

            // If no code exists, nothing to read from CVSecurity
            if (! $orc->code) {
                return;
            }

            // Call CVSecurity get endpoint
            $response = $this->cvSecurityService->getEntranceByCode($orc->code);

            if ($response) {
                Log::debug('Orc successfully read from CVSecurity', [
                    'orc_id' => $orc->id,
                    'cv_code' => $orc->code,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Orc CVSecurity sync on read', [
                'orc_id' => $orc->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Prepare Orc data for CVSecurity API
     */
    protected function prepareOrcData(Orc $orc): array
    {
        // Load the korpus relationship to get the parent code
        $orc->load('korpus');

        $data = [
            'name' => "{$orc->number} орц",
            'remark' => "Орцны дугаар: {$orc->number}",
        ];

        // Set parent_code to the associated Korpus code
        if ($orc->korpus && $orc->korpus->code) {
            $data['parent_code'] = $orc->korpus->code;
        }

        return $data;
    }

    /**
     * Extract code from CVSecurity response
     */
    protected function extractCodeFromResponse(?object $response): ?string
    {
        if (! $response) {
            return null;
        }

        // Try to extract code from different possible response structures
        if (isset($response->area->code)) {
            return $response->area->code;
        }

        if (isset($response->department->code)) {
            return $response->department->code;
        }

        if (isset($response->code)) {
            return $response->code;
        }

        return null;
    }

    /**
     * Update Orc code field
     */
    protected function updateOrcCode(Orc $orc, ?string $code): void
    {
        try {
            // Update without triggering observers to avoid infinite loops
            $orc->updateQuietly([Orc::CODE => $code]);
        } catch (\Exception $e) {
            Log::error('Failed to update Orc code', [
                'orc_id' => $orc->id,
                'code' => $code,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
