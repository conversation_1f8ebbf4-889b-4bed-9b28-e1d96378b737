<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\NehemjlehTohirgoo
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\NehemjlehTohirgooDtl> $nehemjleh_tohirgoo_dtls
 * @property-read int|null $nehemjleh_tohirgoo_dtls_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Uilchilgee> $uilchilgees
 * @property-read int|null $uilchilgees_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo query()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgoo whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class NehemjlehTohirgoo extends Model
{
    use HasFactory;

    const ID = 'id';

    const NAME = 'name';

    protected $fillable = [
        self::NAME,
    ];

    const RELATION_UILCHILGEES = 'uilchilgees';

    const RELATION_NEHEMJLEH_TOHIRGOO_DTLS = 'nehemjleh_tohirgoo_dtls';

    public function uilchilgees(): BelongsToMany
    {
        return $this->belongsToMany(Uilchilgee::class, 'nehemjleh_tohirgoo_uilchilgee');
    }

    public function nehemjleh_tohirgoo_dtls()
    {
        return $this->hasMany(NehemjlehTohirgooDtl::class);
    }
}
