<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\InvoiceResource\Pages;
use App\Models\Invoice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Гүйлгээ';

    protected static ?string $modelLabel = 'гүйлгээ';

    protected static ?int $navigationSort = 5;

    protected static ?string $slug = 'invoices';

    protected static ?string $navigationGroup = 'Бусад';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('invoice_code')
                            ->label('Invoice_code'),
                        Forms\Components\TextInput::make('sender_invoice_no')
                            ->label('Sender_invoice_no'),
                        Forms\Components\TextInput::make('invoice_description')
                            ->label('Тайлбар'),
                        Forms\Components\TextInput::make('amount')
                            ->label('Дүн'),
                        Forms\Components\TextInput::make('status')
                            ->label('Төлөв'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?Invoice $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Invoice $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Invoice $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Invoice $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('orshin_suugch.last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orshin_suugch.name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orshin_suugch.phone')->label('Утас')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('number')->label('Тоот')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('package.name')->label('Багц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('package_products')->label('Бүтээгдэхүүн')->sortable(),
                Tables\Columns\TextColumn::make('erkh.begin_date')->label('Эхлэх огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('erkh.end_date')->label('Дуусах огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('amount')->label('Үнэ'),
                Tables\Columns\TextColumn::make('status')->label('Төлөв')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'completed' => 'success',
                        'canceled' => 'danger',
                    })->sortable()->searchable(),
            ])
            ->filters([
            ])
            ->actions([
            ])
            ->bulkActions([

            ])
            ->emptyStateActions([

            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->orderByDesc('created_at');
    }
}
