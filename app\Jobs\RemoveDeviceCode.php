<?php

namespace App\Jobs;

use App\Enums\CQPEnum;
use App\Models\Erkh;
use App\Services\ChirpStackService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class RemoveDeviceCode implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $queueUniqueId;

    protected $devEui;

    protected $deviceCode;

    protected $erkhId;

    /**
     * Create a new job instance.
     *
     * @param  $phone
     * @param  $text
     */
    public function __construct($devEui, $deviceCode, $erkhId)
    {
        $this->queueUniqueId = $devEui.'-'.$deviceCode;
        $this->devEui = $devEui;
        $this->deviceCode = $deviceCode;
        $this->erkhId = $erkhId;
    }

    public function uniqueId()
    {
        return $this->queueUniqueId;
    }

    public function handle(ChirpStackService $chirpService): void
    {
        try {
            DB::beginTransaction();
            $data = base64_encode("0;$this->deviceCode");
            $chirpService->postDeviceQueue($this->devEui, $data, CQPEnum::KEY);
            $erkh = Erkh::find($this->erkhId);
            $erkh->removed_device_code = true;
            $erkh->save();
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollback();
            throw $th;
        }
    }
}
