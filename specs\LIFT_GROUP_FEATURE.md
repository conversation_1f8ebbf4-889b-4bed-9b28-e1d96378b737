# Lift Group Management System

## Current State

The Lift Group Management System is fully implemented and operational. This system allows administrators to manage elevator access control by grouping lift devices and configuring davhar (floor) level permissions through integration with CVSecurity biometric systems using a sophisticated offset-based mapping system.

## System Overview

The system provides:
1. **Device Grouping**: Multiple lift devices grouped together for unified management
2. **Davhar-Device Permission Matrix**: Visual permission matrix with offset-based floor mapping
3. **CVSecurity Integration**: Real-time synchronization with external biometric systems
4. **Hierarchical Management**: Groups belong to specific Orcs (building entrances)
5. **Offset-based Floor Mapping**: Flexible mapping between building floors (davhars) and CVSecurity device floors

## Architecture

### Data Models

#### LiftGroup Model
```php
// app/Models/LiftGroup.php
class LiftGroup extends Model
{
    protected $fillable = ['orc_id', 'name'];

    // Relationships
    public function orc(): BelongsTo
    public function devices(): HasMany
    public function liftGroupDeviceDavhars(): HasManyThrough
}
```

#### LiftGroupDevice Model
```php
// app/Models/LiftGroupDevice.php
class LiftGroupDevice extends Model
{
    protected $fillable = ['lift_group_id', 'device_id'];

    public function liftGroup(): BelongsTo
    public function liftGroupDeviceDavhars(): HasMany
}
```

#### LiftGroupDeviceDavhar Model
```php
// app/Models/LiftGroupDeviceDavhar.php
class LiftGroupDeviceDavhar extends Model
{
    protected $fillable = [
        'lift_group_device_id',
        'davhar_id',
        'code',
        'offset',
        'enabled'
    ];

    public function liftGroupDevice(): BelongsTo
    public function davhar(): BelongsTo
}
```

### Database Schema

#### lift_groups Table
- `id` - Primary key
- `orc_id` - Foreign key to orcs table (restricts on delete)
- `name` - Group name
- `timestamps`

#### lift_group_devices Table
- `id` - Primary key
- `lift_group_id` - Foreign key to lift_groups
- `device_id` - CVSecurity device identifier (unique - each device can only belong to one lift group)
- `timestamps`

#### lift_group_device_davhars Table
- `id` - Primary key
- `lift_group_device_id` - Foreign key to lift_group_devices (restricts on delete)
- `davhar_id` - Foreign key to davhars table (restricts on delete)
- `code` - Optional code field
- `offset` - Integer offset for floor mapping (default: 0)
- `enabled` - Boolean flag for permission status (default: false)
- `timestamps`
- **Unique constraint**: `[lift_group_device_id, davhar_id]`

#### lift_group_davhar_toots Table (Optional)
- `id` - Primary key
- `lift_group_davhar_id` - Foreign key to lift_group_device_davhars
- `toot_id` - Foreign key to toots table
- `timestamps`
- **Unique constraint**: `[lift_group_davhar_id, toot_id]`

### Service Layer

#### CvSecurityServiceExt
Located in `app/Services/CvSecurityService/CvSecurityServiceExt.php`

**Key Methods:**
- `getDeviceList(int $skip = 0, int $limit = 100, ?string $area_code = null, bool $useDemo = false): ?array` - Retrieves device list with floor data from CVSecurity
- `saveLiftFloorChanges(LiftGroup $liftGroup, array $floorData): bool` - Saves permission changes via batch update
- `getLiftGroupFloorPermissions(LiftGroup $liftGroup): array` - Gets current floor permissions for lift group devices

## User Interface

### Admin Panel Integration

#### Filament Resources
- **OrcResource**: Enhanced with LiftGroups relation manager
- **LiftGroupsRelationManager**: Main interface for group management
- **Custom Pages**:
  - `CreateLiftGroup`: Group creation with device selection
  - `EditLiftGroup`: Group editing with permission matrix
  - `ViewLiftGroups`: Overview of all groups for an Orc

#### Davhar-Device Permission Matrix UI
The system uses a custom `DavharDeviceTable` component that displays:
- **Rows**: Building floors (davhars) from `davhars` table with floor numbers
- **Device Columns**: Each selected CVSecurity device has 3 sub-columns:
  - **Enabled**: Checkbox for permission enable/disable (saved to `lift_group_device_davhars.enabled`)
  - **Floor Name**: CVSecurity floor name from API (displays matching `floor.name` based on mapping)
  - **Offset**: Number input for floor mapping (saved to `lift_group_device_davhars.offset`, default: 0)

#### Floor Mapping Logic
The system uses an offset-based mapping formula:
```
CVSecurity Floor Number + Offset = Davhar Number
```

**Example Table Structure:**
```
+-------------------------+----------------------------------------+
| Floor                   |                  Device                |
|                         +----------------------------+-----------|
|                         | Enabled | Floor Name       | Offset    |
+-------------------------+---------+------------------+-----------+
| Floor 1                 | ✓       | Floor 1 CVSec    | 0         |
| Floor 2                 |         |                  |           |
| Floor 3                 |         |                  |           |
| Floor 4                 | ✓       | Floor 2 CVSec    |  2        |
| Floor 5                 | ✓       | Floor 3 CVSec    |  2        |
+-------------------------+---------+------------------+-----------+
```

### Current User Workflow

1. **Create Lift Group**:
   - Navigate to Orc management
   - Select "Lift Groups" relation
   - Click "New" to create group
   - Enter group name
   - Select devices from CVSecurity integration (devices not already assigned to other groups)
   - Configure davhar-device permissions via matrix with offset mapping
   - Save group

2. **Manage Permissions**:
   - Davhar-Device matrix shows all building floors (davhars) vs selected devices
   - For each davhar-device combination:
     - Toggle enabled/disabled checkbox
     - View matched CVSecurity floor name with color coding:
       - **Green**: Enabled CVSecurity floor found
       - **Orange**: Disabled CVSecurity floor found
       - **Red**: No matching CVSecurity floor found
     - Adjust offset value to map to different CVSecurity floors
   - Changes are saved to database and can sync to CVSecurity system
   - Real-time validation and visual feedback

## CVSecurity Integration

### Device Data Structure
```json
{
  "id": "40288149975e5d9401977699a4780155",
  "dev_alias": "*************",
  "device_name": "EC10",
  "enabled": true,
  "ip_address": "*************",
  "floors": [
    {
      "id": "40288149975e5d9401977699a487017e",
      "name": "*************(Floor1)",
      "floor_no": 1,
      "enabled": true,
      "dev_id": "40288149975e5d9401977699a4780155"
    }
  ]
}
```

### Service Integration
- **Health Checking**: `isServiceAvailable()` before operations
- **Error Handling**: Graceful degradation when CVSecurity unavailable
- **Logging**: Comprehensive logging of all operations
- **Real-time Updates**: Live data fetching from CVSecurity API

## DavharDeviceTable Component

### Component Structure
The `DavharDeviceTable` is a custom Filament field component that provides the davhar-device permission matrix interface.

**Location**: `app/Filament/Components/DavharDeviceTable.php`
**View**: `resources/views/filament/components/davhar-device-table.blade.php`

### Key Features
1. **Dynamic Device Loading**: Accepts device IDs and fetches device data from CVSecurity
2. **Floor Matching Logic**: Implements offset-based floor mapping between davhars and CVSecurity floors
3. **Real-time Validation**: Live validation of davhar-device combinations
4. **Interactive UI**: Alpine.js powered interface with live updates

### Table Structure
```
+-------------------------+----------------------------------------+
| Давхар                  |          192.168.0.122 (EC10)          |
|                         +----------------------------+-----------|
|                         | Enabled | Floor Name       | Offset    |
+-------------------------+---------+------------------+-----------+
| Давхар 1                | ✓       | Floor 1 (CVSec)  | 0         |
| Давхар 2                |         |                  |           |
| Давхар 3                |         |                  |           |
| Давхар 4                | ✓       | Floor 2 (CVSec)  | 2         |
| Давхар 5                | ✓       | Floor 3 (CVSec)  | 2         |
| Давхар 6                | ✓       | Floor 4 (CVSec)  | 2         |
| Давхар 7                | ✓       | Floor 5 (CVSec)  | 2         |
| Давхар 8                | ✓       | Floor 6 (CVSec)  | 2         |
| Давхар 9                | ✗       | Floor 7 (CVSec)  | 2         |
| Давхар 10               | ✗       | Floor 8 (CVSec)  | 2         |
| Давхар 11               | ✗       | Floor 9 (CVSec)  | 2         |
| Давхар 12               | ✗       | Floor 10( CVSec) | 2         |
| Давхар 13               |         |                  |           |
| Давхар 14               |         |                  |           |
| Давхар 15               |         |                  |           |
| Давхар 16               |         |                  |           |
+-------------------------+---------+------------------+-----------+
```

### Component Methods
- `getDavhars()`: Returns all davhars from database ordered by korpus and number
- `getDevices()`: Fetches device data from CVSecurity service with floor information
- `getFloorMatchingData()`: Builds floor matching data for all davhar-device combinations using offset calculation
- `isValidCombination()`: Validates if a davhar-device combination has a valid enabled CVSecurity floor match

### Data Sources
- **Davhars**: Database table `davhars` (building floors with `number` field)
- **CVSecurity Floors**: API response with `floor_no`, `name`, `enabled` fields
- **Saved Data**: `lift_group_device_davhars` table stores `enabled` and `offset` values only

### Alpine.js State Management
- `permissions`: Tracks enabled/disabled state for each davhar-device combination
- `offsets`: Stores offset values for floor mapping
- `devices`: Current selected devices
- `floorMatchingData`: CVSecurity floor data for validation and display

## Implementation Files

### Core Component Files
- **`app/Filament/Components/DavharDeviceTable.php`**: Main component class with data fetching logic
- **`resources/views/filament/components/davhar-device-table.blade.php`**: Alpine.js powered UI template

### Page Controllers
- **`app/Filament/Resources/Admin/LiftGroupResource/Pages/CreateLiftGroup.php`**: Lift group creation with device selection and permission setup
- **`app/Filament/Resources/Admin/LiftGroupResource/Pages/EditLiftGroup.php`**: Lift group editing with permission management

### Model Files
- **`app/Models/LiftGroup.php`**: Main lift group model with relationships
- **`app/Models/LiftGroupDevice.php`**: Device assignment model
- **`app/Models/LiftGroupDeviceDavhar.php`**: Davhar-device permission mapping model

### Database Migrations
- **`2023_08_08_000010_create_lift_groups_table.php`**: Base lift groups table
- **`2025_06_17_052958_create_lift_group_device_table.php`**: Device assignment table
- **`2025_06_27_025329_create_lift_group_device_davhars_table.php`**: Permission mapping table
- **`2025_07_01_102255_add_enabled_to_lift_group_device_davhars_table.php`**: Added enabled flag

## Technical Implementation

### Form Components
- **Device Selection**: Dynamic dropdown populated from CVSecurity (excludes devices already assigned to other lift groups)
- **DavharDeviceTable Component**: Custom Alpine.js component for davhar-device permission matrix
- **Real-time Updates**: Live form updates when adding/removing devices
- **Offset Inputs**: Number inputs with debounced updates for floor mapping

### Data Flow
1. User selects devices → CVSecurity API call for device list with floor data
2. Load davhars from database → Get all building floors (`davhars` table)
3. Matrix populated → Cross-reference davhars with selected devices:
   - **Rows**: All davhars from database
   - **Columns**: Selected CVSecurity devices with 3 sub-columns each
4. Floor matching logic:
   - For each davhar-device combination, calculate: `CVSecurity floor_no + offset = davhar.number`
   - Display matching CVSecurity floor name if found
   - Show color-coded status (enabled/disabled/not found)
5. User interactions:
   - Toggle enabled checkbox → Update local permissions state
   - Adjust offset → Recalculate floor mappings and update floor name display
   - Real-time validation and visual feedback
6. Form submission → Persist to database:
   - Create/update `lift_group_devices` records
   - Create/update `lift_group_device_davhars` records with `enabled` and `offset` values
   - Remove records for disabled combinations

### Error Handling
- **Service Unavailable**: Clear messaging when CVSecurity offline
- **API Failures**: Notifications with specific error messages
- **Data Validation**: Form validation before submission
- **Transaction Safety**: Database operations wrapped in try-catch


## Configuration

### Service Configuration
CVSecurity service settings in `config/services.php`:
```php
'cvsecurity' => [
    'host' => env('CVSECURITY_HOST'),
    'port' => env('CVSECURITY_PORT'),
    'ssl' => env('CVSECURITY_SSL', false),
],
```

### Environment Variables
```env
CVSECURITY_HOST=*************
CVSECURITY_PORT=8080
CVSECURITY_SSL=false
```

## Testing

### Test Coverage Areas
- **Model Relationships**: LiftGroup ↔ Orc, LiftGroup ↔ LiftGroupDevice
- **Service Integration**: CVSecurity API calls and error handling
- **Form Validation**: Device selection and permission matrix
- **Database Operations**: CRUD operations with proper constraints

### Testing Commands
```bash
# Run specific tests for lift groups
php artisan test --filter LiftGroup

# Test CVSecurity integration
php artisan test:orc-cv-security
```

## Monitoring and Logging

### Log Events
- Device list retrieval from CVSecurity
- Floor permission changes
- Service availability checks
- Error conditions and recovery

### Log Context
```php
Log::info('Updated floor state in CvSecurity', [
    'device_id' => $deviceId,
    'floor_no' => $floorNo,
    'floor_id' => $floorId,
    'enabled' => $enabled
]);
```

## Future Enhancements

### Planned Features
1. **CVSecurity Synchronization**: Implement actual floor permission sync to CVSecurity devices
2. **Bulk Operations**: Import/export device configurations and permission templates
3. **Permission Templates**: Reusable davhar-device permission sets
4. **Audit Trail**: Track all permission changes with user attribution
5. **Real-time Status**: Live device status monitoring and floor state validation
6. **Advanced Offset Management**: Bulk offset adjustments and validation tools
7. **Toot Integration**: Complete implementation of `lift_group_davhar_toots` for resident access

### Integration Opportunities
- **Enhanced CVSecurity Sync**: Real-time bidirectional synchronization
- **ChirpStack Integration**: LoRaWAN device management for building systems
- **Notification System**: Real-time alerts for access events and permission changes
- **Reporting Dashboard**: Usage analytics, access reports, and permission audit logs
- **Mobile Interface**: Mobile app for on-site permission management

## Troubleshooting

### Common Issues

**CVSecurity Connection Failures**
- Check network connectivity to CVSecurity server
- Verify service configuration in environment variables
- Review logs for specific API error messages

**DavharDeviceTable Not Loading**
- Ensure devices are properly selected before matrix display
- Check JavaScript console for Alpine.js errors
- Verify CVSecurity returns valid floor data
- Check that davhars exist in the database

**Floor Mapping Issues**
- Verify offset calculations: `CVSecurity Floor + Offset = Davhar Number`
- Check that CVSecurity floors exist for the mapping
- Ensure offset values are within reasonable range (-10 to +10)

**Permission State Issues**
- Check that `lift_group_device_davhars` records are created for enabled combinations
- Verify that disabled combinations are properly removed from database
- Ensure unique constraints are not violated

**Database Constraint Violations**
- Ensure Orc exists before creating LiftGroup
- Check device_id format matches CVSecurity expectations
- Verify foreign key relationships are properly maintained

### Debug Commands
```bash
# Check CVSecurity service status
php artisan tinker
>>> app(App\Services\CvSecurityService\CvSecurityServiceExt::class)->isServiceAvailable()

# Inspect device data
>>> app(App\Services\CvSecurityService\CvSecurityServiceExt::class)->getDeviceList()
```

## Security Considerations

### Access Control
- Lift group management restricted to admin users
- Device access controlled through CVSecurity permissions
- Audit logging for all permission changes

### Data Validation
- Input sanitization for device IDs and floor numbers
- Foreign key constraints prevent orphaned records
- Service availability checks before external API calls

### Error Information
- Sensitive CVSecurity details not exposed in client errors
- Structured logging without exposing credentials
- Graceful degradation when external services unavailable