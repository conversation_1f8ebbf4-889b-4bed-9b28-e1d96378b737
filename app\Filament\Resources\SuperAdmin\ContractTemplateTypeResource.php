<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\ContractTemplateTypeResource\Pages;
use App\Models\ContractTemplateType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ContractTemplateTypeResource extends Resource
{
    protected static ?string $model = ContractTemplateType::class;

    protected static ?string $navigationIcon = 'heroicon-o-document';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Гэрээний загварын төрөл';

    protected static ?string $modelLabel = 'гэрээний загварын төрөл';

    protected static ?int $navigationSort = 4;

    protected static ?string $slug = 'contractTemplateTypes';

    protected static ?string $navigationGroup = 'Гэрээ';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?ContractTemplateType $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (ContractTemplateType $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (ContractTemplateType $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?ContractTemplateType $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractTemplateTypes::route('/'),
            'create' => Pages\CreateContractTemplateType::route('/create'),
            'edit' => Pages\EditContractTemplateType::route('/{record}/edit'),
        ];
    }
}
