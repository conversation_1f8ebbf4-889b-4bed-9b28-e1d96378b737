<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lift_group_device_davhars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lift_group_device_id')->constrained('lift_group_devices')->restrictOnDelete();
            $table->foreignId('davhar_id')->constrained('davhars')->restrictOnDelete();
            $table->string('code')->nullable();
            $table->integer('offset')->default(0);
            $table->timestamps();

            $table->unique(['lift_group_device_id', 'davhar_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lift_group_device_davhars');
    }
};
