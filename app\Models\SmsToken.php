<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\SmsToken
 *
 * @mixin IdeHelperSmsToken
 *
 * @property int $id
 * @property string $phone
 * @property string $code
 * @property string $expired_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken query()
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SmsToken whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SmsToken extends Model
{
    use HasFactory;

    const TABLE = 'sms_tokens';

    const PHONE = 'phone';

    const CODE = 'code';

    const EXPIRED_AT = 'expired_at';

    protected $fillable = [
        self::PHONE,
        self::CODE,
        self::EXPIRED_AT,
    ];
}
