<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BpayInvoice extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'total_amount' => $this->total_amount,
            'is_org' => $this->isOrg,
            'vat_info' => $this->vatInfo,
            'status_id' => $this->statusId,
            'customer_id' => $this->customerId,
            'payment_method_id' => $this->paymentMethodId,
            'transaction_method_id' => $this->transactionMethodId,
            'merchant_id' => $this->MerchantId,
            'provider_merchant_id' => $this->providerMerchantId,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
            'bills' => BpayBill::collection($this->bills),
            'payment_sub_groups' => $this->paymentSubGroups,
        ];
    }
}
