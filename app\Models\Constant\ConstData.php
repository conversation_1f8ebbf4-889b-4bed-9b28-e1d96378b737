<?php

namespace App\Models\Constant;

class ConstData
{
    const AUTH_EXCEPTION = 'Auth_Exception: ';

    const INFO_EXCEPTION = 'Info_Exception: ';

    const SYSTEM_EXCEPTION = 'System_Exception: ';

    const QPAY_EXCEPTION = 'Qpay_Exception: ';

    const BPAY_EXCEPTION = 'Bpay_Exception: ';

    const ORSHIN_SUUGCH_EXCEPTION = 'Orshin_Suugch_Exception: ';

    const ERKH_EXCEPTION = 'Erkh_Exception: ';

    const ROLE_SUPER_ADMIN = 'superadmin';

    const ROLE_ADMIN = 'admin';

    const STATUS_SUCCESS = 'success';

    const STATUS_PROCESSING = 'processing';

    const STATUS_ERROR = 'error';

    const INVOICE_STATUS_COMPLETED = 'completed';

    const INVOICE_STATUS_PENDING = 'pending';

    const INVOICE_STATUS_CANCELED = 'canceled';

    const LOG_OPEN_DOOR_STATUS_PENDING = 'pending';

    const LOG_OPEN_DOOR_STATUS_SUCCESS = 'success';

    const LOG_OPEN_DOOR_STATUS_ERROR = 'error';

    const MSG_SUCCESS = 'Амжилттай';

    const MSG_PROCESSING = 'Хийгдэж байна';

    const MSG_ERROR = 'Алдаатай';

    const CALLPRO_HOST = 'CALLPRO_HOST';

    const CALLPRO_KEY = 'CALLPRO_KEY';

    const CALLPRO_PHONE_NUMBER = 'CALLPRO_PHONE_NUMBER';

    const CHIRP_STACK_TENANT_NAME = 'CHIRP_STACK_TENANT_NAME';

    const CHIRP_STACK_APP_NAME = 'CHIRP_STACK_APP_NAME';

    const CHIRP_STACK_HOST = 'CHIRP_STACK_HOST';

    const CHIRP_STACK_TOKEN = 'CHIRP_STACK_TOKEN';

    const BPAY_URL = 'BPAY_URL';

    const BPAY_USER_NAME = 'BPAY_USER_NAME';

    const BPAY_PASSWORD = 'BPAY_PASSWORD';

    const BPAY_OUR_EMAIL = 'BPAY_OUR_EMAIL';

    const CV_SECURITY_HOST = 'CV_SECURITY_HOST';

    const CV_SECURITY_PORT = 'CV_SECURITY_PORT';

    const CV_SECURITY_EXT_HOST = 'CV_SECURITY_EXT_HOST';

    const CV_SECURITY_EXT_PORT = 'CV_SECURITY_EXT_PORT';

    const CV_SECURITY_API_KEY = 'CV_SECURITY_API_KEY';

    const STATUS = 'status';

    const MESSAGE = 'message';

    const VALUE = 'value';

    const TOKEN = 'token';

    const ATTACHMENT = 'attachment';

    const GET = 'get';

    const POST = 'post';

    const ID = 'id';

    const NAME = 'name';

    const APP_ENV = 'APP_ENV';

    const APP_URL = 'APP_URL';

    const DB_DATABASE = 'DB_DATABASE';

    const LOCAL = 'local';

    const PRODUCTION = 'production';

    const QPAY_INVOICE_CODE = 'QPAY_INVOICE_CODE';

    const QPAY_HOST = 'QPAY_HOST';

    const QPAY_USERNAME = 'QPAY_USERNAME';

    const QPAY_PASSWORD = 'QPAY_PASSWORD';

    const MINIO_ACCESS_KEY_ID = 'MINIO_ACCESS_KEY_ID';

    const MINIO_SECRET_ACCESS_KEY = 'MINIO_SECRET_ACCESS_KEY';

    const MINIO_DEFAULT_REGION = 'MINIO_DEFAULT_REGION';

    const MINIO_BUCKET = 'MINIO_BUCKET';

    const MINIO_URL = 'MINIO_URL';

    const MINIO_ENDPOINT = 'MINIO_ENDPOINT';

    const MINIO_USE_PATH_STYLE_ENDPOINT = 'MINIO_USE_PATH_STYLE_ENDPOINT';

    public static function successMSG($value = null)
    {
        return response()->json([
            'status' => self::STATUS_SUCCESS,
            'message' => 'Амжилттай',
            'value' => (string) $value,
        ], 200);
    }
}
