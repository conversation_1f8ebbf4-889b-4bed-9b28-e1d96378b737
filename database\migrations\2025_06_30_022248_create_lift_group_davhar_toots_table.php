<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lift_group_davhar_toots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lift_group_davhar_id')->constrained('lift_group_device_davhars')->restrictOnDelete();
            $table->foreignId('toot_id')->constrained('toots')->restrictOnDelete();
            $table->timestamps();

            $table->unique(['lift_group_davhar_id', 'toot_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lift_group_davhar_toots');
    }
};
