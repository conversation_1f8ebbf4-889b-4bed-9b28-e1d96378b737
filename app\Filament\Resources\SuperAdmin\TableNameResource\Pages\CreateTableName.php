<?php

namespace App\Filament\Resources\SuperAdmin\TableNameResource\Pages;

use App\Filament\Resources\SuperAdmin\TableNameResource;
use App\Models\ColumnName;
use App\Services\InfoService;
use Filament\Resources\Pages\CreateRecord;

class CreateTableName extends CreateRecord
{
    protected static string $resource = TableNameResource::class;

    protected function afterCreate(): void
    {
        $record = $this->record;
        $service = resolve(InfoService::class);
        $columns = $service->getColumnNames($record->getOrignalTableName());
        $newColumns = [];
        foreach ($columns as $key => $column) {
            if ($column == 'id') {
                continue;
            }
            $newColumn = new ColumnName([
                ColumnName::NAME => $column,
                ColumnName::DESCRIPTION => $column,
            ]);
            $newColumns[] = $newColumn;
        }
        $record->column_names()->saveMany($newColumns);
    }
}
