<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\Invoice
 *
 * @mixin IdeHelperInvoice
 *
 * @property int $id
 * @property int $orshin_suugch_id
 * @property int $package_id
 * @property string $package_name
 * @property string|null $invoice_id
 * @property string|null $invoice_code
 * @property string|null $sender_invoice_no
 * @property string|null $invoice_receiver_code
 * @property string|null $invoice_description
 * @property string|null $amount
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $package_products
 * @property int|null $valid_day
 * @property int|null $korpus_id
 * @property int|null $number
 * @property-read \App\Models\Erkh|null $erkh
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoiceMember> $invoice_members
 * @property-read int|null $invoice_members_count
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 * @property-read \App\Models\Package|null $package
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereInvoiceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereInvoiceDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereInvoiceReceiverCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice wherePackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice wherePackageName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice wherePackageProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereSenderInvoiceNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereValidDay($value)
 *
 * @mixin \Eloquent
 */
class Invoice extends Model
{
    use HasFactory;

    const ID = 'id';

    const ORSHIN_SUUGCH_ID = 'orshin_suugch_id';

    const KORPUS_ID = 'korpus_id';

    const NUMBER = 'number';

    const PACKAGE_ID = 'package_id';

    const PACKAGE_NAME = 'package_name';

    const PACKAGE_PRODUCTS = 'package_products';

    const INVOICE_CODE = 'invoice_code';

    const SENDER_INVOICE_NO = 'sender_invoice_no';

    const INVOICE_RECEIVER_CODE = 'invoice_receiver_code';

    const INVOICE_DESCRIPTION = 'invoice_description';

    const AMOUNT = 'amount';

    const STATUS = 'status';

    const INVOICE_ID = 'invoice_id';

    const VALID_DAY = 'valid_day';

    const QR_TEXT = 'qr_text';

    const QR_IMAGE = 'qr_image';

    const QPAY_SHORTURL = 'qPay_shortUrl';

    const URLS = 'urls';

    const RELATION_PACKAGE = 'package';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::PACKAGE_ID,
        self::PACKAGE_NAME,
        self::PACKAGE_PRODUCTS,
        self::INVOICE_CODE,
        self::SENDER_INVOICE_NO,
        self::INVOICE_RECEIVER_CODE,
        self::INVOICE_DESCRIPTION,
        self::AMOUNT,
        self::STATUS,
        self::INVOICE_ID,
        self::VALID_DAY,
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    public function erkh(): HasOne
    {
        return $this->hasOne(Erkh::class);
    }

    public function invoice_members(): HasMany
    {
        return $this->hasMany(InvoiceMember::class);
    }
}
