<?php

namespace App\Providers;

use App\Models\Bair;
use App\Models\Constant\ConstData;
use App\Models\Korpus;
use App\Models\LiftGroupDeviceDavhar;
use App\Models\Orc;
use App\Models\OrshinSuugchToot;
use App\Models\Sukh;
use App\Observers\BairObserver;
use App\Observers\KorpusObserver;
use App\Observers\LiftGroupDeviceDavharObserver;
use App\Observers\OrcObserver;
use App\Observers\OrshinSuugchTootObserver;
use App\Observers\SukhObserver;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        $this->app->register(TelescopeServiceProvider::class);

        // Register CvSecurityService
        $this->app->singleton(\App\Services\CvSecurityService\CvSecurityService::class);

        // Register CvSecurityServiceExt
        $this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

        // Register SukhSyncService
        $this->app->singleton(\App\Services\SukhSyncService::class);

        // Register KorpusSyncService
        $this->app->singleton(\App\Services\KorpusSyncService::class);

        // Register BairSyncService
        $this->app->singleton(\App\Services\BairSyncService::class);

        // Register OrcSyncService
        $this->app->singleton(\App\Services\OrcSyncService::class);

        // Register LiftGroupDeviceDavharSyncService
        $this->app->singleton(\App\Services\LiftGroupDeviceDavharSyncService::class);

        // Register DoorNumberingService
        $this->app->singleton(\App\Services\DoorNumberingService::class);

        // Register OrshinSuugchSyncService with explicit dependency injection
        $this->app->singleton(\App\Services\OrshinSuugchSyncService::class, function ($app) {
            return new \App\Services\OrshinSuugchSyncService(
                $app->make(\App\Services\CvSecurityService\CvSecurityService::class),
                $app->make(\App\Services\CvSecurityService\CvSecurityServiceExt::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (! $this->app->environment(ConstData::LOCAL)) {
            URL::forceScheme('https');
        }

        // Register model observers
        Sukh::observe(SukhObserver::class);
        Korpus::observe(KorpusObserver::class);
        Bair::observe(BairObserver::class);
        Orc::observe(OrcObserver::class);
        LiftGroupDeviceDavhar::observe(LiftGroupDeviceDavharObserver::class);
        OrshinSuugchToot::observe(OrshinSuugchTootObserver::class);

        Scramble::routes(function (Route $route) {
            return Str::startsWith($route->uri, 'api/');
        });

        Scramble::afterOpenApiGenerated(function (OpenApi $openApi) {
            $openApi->secure(
                SecurityScheme::http('bearer')
            );
        });
    }
}
