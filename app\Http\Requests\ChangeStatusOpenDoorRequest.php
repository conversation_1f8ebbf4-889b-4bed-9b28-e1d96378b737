<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChangeStatusOpenDoorRequest extends FormRequest
{
    const PARAMETER_LOG_NO = 'log_no';

    const PARAMETER_DEV_EUI = 'dev_eui';

    const PARAMETER_STATUS = 'status';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_LOG_NO => 'required|exists:log_open_doors,log_no',
            self::PARAMETER_DEV_EUI => 'required|exists:log_open_doors,dev_eui',
            self::PARAMETER_STATUS => 'required|string',
        ];
    }
}
