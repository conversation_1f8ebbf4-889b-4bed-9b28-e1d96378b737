<?php

namespace App\Services;

use App\Models\Constant\ConstData;
use App\Models\DeviceConfig;
use Illuminate\Support\Facades\Http;

class ChirpStackService
{
    public function getTenantId()
    {
        $tenantName = config('services.chirp.tenant_name');
        $param = ['limit' => 1, 'search' => $tenantName];
        $response = $this->callChirpApiWithToken('tenants', ConstData::GET, $param);

        return isset($response->result) && ! empty($response->result) ? $response->result[0]->id : null;
    }

    public function getAppId()
    {
        $tenantId = $this->getTenantId();
        if (! isset($tenantId)) {
            return null;
        }
        $appName = config('services.chirp.app_name');
        $param = ['limit' => 1, 'tenant_id' => $tenantId, 'search' => $appName];
        $response = $this->callChirpApiWithToken('applications', ConstData::GET, $param);

        return isset($response->result) && ! empty($response->result) ? $response->result[0]->id : null;
    }

    public function getDeviceProfiles($limit)
    {
        $tenantId = $this->getTenantId();
        if (! isset($tenantId)) {
            return [];
        }
        $param = ['limit' => $limit, 'tenant_id' => $tenantId];
        $response = $this->callChirpApiWithToken('device-profiles', ConstData::GET, $param);

        return collect($response->result)->pluck(ConstData::NAME, ConstData::ID);
    }

    public function postDevice($deviceHdr, $deviceDtl)
    {
        $deviceConfig = DeviceConfig::find($deviceHdr->device_config_id);
        $appId = $this->getAppId();
        $param = [
            'device' => [
                'applicationId' => $appId,
                'deviceProfileId' => $deviceConfig->pro_id,
                'description' => $deviceHdr->description,
                'devEui' => $deviceDtl->dev_eui,
                'joinEui' => $deviceDtl->join_eui,
                'name' => $deviceDtl->name,
                'isDisabled' => $deviceHdr->is_disabled,
                'skip_fcnt_check' => $deviceHdr->skip_fcnt_check,
            ],
        ];
        // Create the given device.
        $response = $this->callChirpApiWithToken('devices', ConstData::POST, $param);
        $param = [
            'deviceKeys' => ['nwkKey' => $deviceDtl->nwk_key],
        ];
        // Create the given device-keys.
        $response = $this->callChirpApiWithToken("devices/$deviceDtl->dev_eui/keys", ConstData::POST, $param);

        return $response;
    }

    public function postDeviceQueue($devEui, $data, $fport)
    {
        $chripStackHost = config('services.chirp.host');
        $token = config('services.chirp.token');
        $url = "$chripStackHost/api/devices/$devEui/queue";
        $body = [
            'queueItem' => [
                'confirmed' => false,
                'data' => $data,
                'fPort' => $fport,
            ],
        ];
        $response = Http::withToken($token)->post($url, $body);

        return json_decode($response);
    }

    public function callChirpApiWithToken($apiName, $methodType, $param)
    {
        $chripStackHost = config('services.chirp.host');
        $token = config('services.chirp.token');
        $url = "$chripStackHost/api/$apiName";
        $response = null;
        switch ($methodType) {
            case ConstData::GET:
                $response = Http::withToken($token)->get($url, $param);
                break;
            case ConstData::POST:
                $response = Http::withToken($token)->post($url, $param);
                break;
        }

        return json_decode($response);
    }
}
