<?php

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDatabaseSequences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:fix-sequences';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix PostgreSQL auto-increment sequences after manual ID insertion';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing database sequences...');

        // Fix users table sequence
        $maxUserId = User::max('id') ?? 0;
        DB::statement("SELECT setval('users_id_seq', ".($maxUserId + 1).', false);');
        $this->info('Fixed users_id_seq to start from '.($maxUserId + 1));

        // Fix roles table sequence
        $maxRoleId = Role::max('id') ?? 0;
        DB::statement("SELECT setval('roles_id_seq', ".($maxRoleId + 1).', false);');
        $this->info('Fixed roles_id_seq to start from '.($maxRoleId + 1));

        $this->info('Database sequences fixed successfully!');

        return 0;
    }
}
