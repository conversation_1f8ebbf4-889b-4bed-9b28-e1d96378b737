<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\KorpusResource\Pages;
use App\Filament\Resources\Admin\KorpusResource\RelationManagers;
use App\Models\Bair;
use App\Models\Korpus;
use App\Services\DoorNumberingService;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class KorpusResource extends Resource
{
    protected static ?string $model = Korpus::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationLabel = 'Блокууд';

    protected static ?string $pluralModelLabel = 'Блокууд';

    protected static ?string $modelLabel = 'блок';

    protected static ?string $slug = 'korpuses';

    // Hide from navigation - only accessible through Bair hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('bair_info')
                            ->label('Байр')
                            ->content(function (?Korpus $record) {
                                if ($record && $record->bair) {
                                    return $record->bair->name;
                                }

                                $bairId = request()->get('bair_id');
                                if ($bairId) {
                                    $bair = \App\Models\Bair::find($bairId);

                                    return $bair ? $bair->name : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Korpus::BAIR_ID)
                            ->default(function () {
                                return request()->get('bair_id');
                            }),

                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $bairId = $get('bair_id');

                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Korpus $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('code')
                            ->label('CV Security код')
                            ->content(fn (Korpus $record): ?string => $record->code ?? 'Тодорхойгүй'),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Korpus $record) => $record === null),

                // Auto-generation section
                Forms\Components\Section::make('Автомат үүсгэх')
                    ->schema([
                        Forms\Components\Toggle::make('auto_generate')
                            ->label('Автомат үүсгэх')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, ?Korpus $record) {
                                if (! $state) {
                                    // Clear auto-generation fields when disabled
                                    $set('generate_floors', true);
                                    $set('numbering_type', null);
                                    $set('number_of_floors', null);
                                    $set('doors_per_floor', null);
                                    $set('digit_multiplier', null);
                                } elseif ($record && $record->davhars()->count() === 0) {
                                    // Automatically enable generate_floors if no davhars exist
                                    $set('generate_floors', true);
                                }
                            }),

                        Forms\Components\Toggle::make('generate_floors')
                            ->label('Давхар шинээр үүсгэх')
                            ->visible(fn (Get $get) => $get('auto_generate'))
                            ->reactive()
                            ->live()
                            ->dehydrated(false)
                            ->default(function (?Korpus $record) {
                                // Always return true for new records
                                if (! $record) {
                                    return true;
                                }
                                // For existing records, check if davhars exist
                                $davharCount = $record->davhars()->count();

                                return $davharCount === 0;
                            })
                            ->afterStateUpdated(function (Get $get, callable $set, $state, ?Korpus $record) {
                                if ($state === true && $record && $record->davhars()->count() > 0) {
                                    Notification::make()
                                        ->warning()
                                        ->title('Давхруудыг устгах')
                                        ->body('Та одоо байгаа давхруудыг устгаж, шинээр үүсгэхдээ итгэлтэй байна уу? Энэ үйлдэл нь буцаагдахгүй.')
                                        ->actions([
                                            \Filament\Notifications\Actions\Action::make('confirm')
                                                ->label('Тийм, үргэлжлүүлэх')
                                                ->button()
                                                ->close(),
                                            \Filament\Notifications\Actions\Action::make('cancel')
                                                ->label('Болих')
                                                ->color('gray')
                                                ->action(function () use ($set) {
                                                    $set('generate_floors', false);
                                                })
                                                ->close(),
                                        ])
                                        ->persistent()
                                        ->send();
                                }
                            }),

                        Forms\Components\TextInput::make('number_of_floors')
                            ->label('Нийт давхарын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get('auto_generate') && $get('generate_floors'))
                            ->required(fn (Get $get) => $get('auto_generate') && $get('generate_floors')),

                        Forms\Components\Select::make('numbering_type')
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                1 => 'Блок бүрээр (Type 1)',
                                3 => 'Давхар бүрээр (Type 3)',
                            ])
                            ->visible(fn (Get $get) => $get('auto_generate'))
                            ->reactive()
                            ->required(fn (Get $get) => $get('auto_generate')),

                        Forms\Components\TextInput::make('doors_per_floor')
                            ->label('Давхар тутмын тоотын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get('auto_generate'))
                            ->required(fn (Get $get) => $get('auto_generate')),

                        Forms\Components\Select::make('digit_multiplier')
                            ->label('Цифрийн үржүүлэгч')
                            ->options([
                                10 => '10 (2 оронтой: 11, 12, 13...)',
                                100 => '100 (3 оронтой: 101, 102, 103...)',
                                1000 => '1000 (4 оронтой: 1001, 1002, 1003...)',
                            ])
                            ->visible(fn (Get $get) => $get('auto_generate') && $get('numbering_type') == 3)
                            ->required(fn (Get $get) => $get('auto_generate') && $get('numbering_type') == 3),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('generate')
                                ->label('Бүх давхар болон тоот үүсгэх')
                                ->color('success')
                                ->icon('heroicon-o-plus-circle')
                                ->visible(fn (Get $get) => $get('auto_generate'))
                                ->requiresConfirmation()
                                ->modalHeading('Бүх давхар болон тоот үүсгэх')
                                ->modalDescription(function (Get $get, ?Korpus $record) {
                                    $description = 'Энэ үйлдэл нь энэ блокийн одоо байгаа тоотуудыг устгаад шинээр үүсгэнэ.';
                                    if ($record && $get('generate_floors') && $record->davhars()->count() > 0) {
                                        $description .= ' Мөн одоо байгаа бүх давхруудыг устгаж, шинээр үүсгэнэ.';
                                    }
                                    $description .= ' Та итгэлтэй байна уу?';

                                    return $description;
                                })
                                ->modalSubmitActionLabel('Тийм, үүсгэх')
                                ->action(function (Korpus $record, Get $get) {
                                    try {
                                        if ($record->orcs()->count() === 0) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэхээс өмнө орцуудыг гараар үүсгэх шаардлагатай.')
                                                ->danger()
                                                ->send();

                                            return;
                                        }

                                        $generateFloors = $get('generate_floors');
                                        $numberingType = $get('numbering_type');
                                        $numberOfFloors = $get('number_of_floors');
                                        $doorsPerFloor = $get('doors_per_floor');
                                        $digitMultiplier = $get('digit_multiplier');

                                        if (empty($numberingType) || empty($doorsPerFloor) || ($generateFloors && empty($numberOfFloors))) {
                                            Notification::make()->title('Алдаа')->body('Автомат үүсгэх талбаруудыг бүрэн бөглөнө үү.')->danger()->send();

                                            return;
                                        }

                                        if ($numberingType == 3 && empty($digitMultiplier)) {
                                            Notification::make()
                                                ->title('Алдаа')
                                                ->body('Давхар бүрээр дугаарлахад цифрийн үржүүлэгч сонгох шаардлагатай.')
                                                ->danger()
                                                ->send();

                                            return;
                                        }

                                        // Check if floors exist when floor generation is disabled
                                        if (! $generateFloors && $record->davhars()->count() === 0) {
                                            Notification::make()
                                                ->title('Давхар олдсонгүй')
                                                ->body('Одоогоор давхар үүсгээгүй байна. Та "Давхар шинээр үүсгэх" сонголтыг идэвхжүүлэх эсвэл давхруудыг гараар үүсгэнэ үү.')
                                                ->warning()
                                                ->persistent()
                                                ->send();

                                            return;
                                        }

                                        $doorNumberingService = app(DoorNumberingService::class);
                                        $doorNumberingService->generateKorpusHierarchy(
                                            $record,
                                            $numberingType,
                                            $doorsPerFloor,
                                            $generateFloors,
                                            $numberOfFloors,
                                            $digitMultiplier
                                        );

                                        Notification::make()
                                            ->title('Амжилттай')
                                            ->body('Давхар болон тоотууд амжилттай үүслээ.')
                                            ->success()
                                            ->send();

                                        return redirect(self::getUrl('edit', ['record' => $record]));
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Алдаа гарлаа')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ]),
                    ])
                    ->columnSpan(['lg' => 3])
                    ->collapsible()
                    ->collapsed()
                    ->hidden(fn (?Korpus $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Блокууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bair.name')->label('Байр')->sortable()->searchable(),

                Tables\Columns\TextColumn::make(Korpus::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орцны тоо'),
                Tables\Columns\TextColumn::make('davhars_count')->counts('davhars')->label('Давхарын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrcsRelationManager::class,
            RelationManagers\DavharsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKorpuses::route('/'),
            'create' => Pages\CreateKorpus::route('/create'),
            'edit' => Pages\EditKorpus::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();

        return parent::getEloquentQuery()->whereHas('bair.sukh', function (Builder $query) use ($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
