<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrcTag
 *
 * @property int $id
 * @property int $orc_id
 * @property string $tag_code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Orc|null $orc
 *
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag whereOrcId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag whereTagCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrcTag whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class OrcTag extends Model
{
    use HasFactory;

    const ID = 'id';

    const ORC_ID = 'orc_id';

    const TAG_CODE = 'tag_code';

    protected $fillable = [
        self::ID,
        self::ORC_ID,
        self::TAG_CODE,
    ];

    public function orc()
    {
        return $this->belongsTo(Orc::class);
    }
}
