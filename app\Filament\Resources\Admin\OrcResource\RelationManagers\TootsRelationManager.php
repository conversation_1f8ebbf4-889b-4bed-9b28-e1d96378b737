<?php

namespace App\Filament\Resources\Admin\OrcResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TootsRelationManager extends RelationManager
{
    protected static string $relationship = 'toots';

    protected static ?string $modelLabel = 'тоот';

    protected static ?string $pluralModelLabel = 'Тоотууд';

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $orc = $this->getOwnerRecord();
        $data['korpus_id'] = $orc->korpus_id;

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('davhar_id')
                    ->label('Давхар')
                    ->relationship('davhar', 'number')
                    ->options(function (RelationManager $livewire) {
                        $korpus = $livewire->getOwnerRecord()->korpus;

                        return $korpus->davhars()->pluck('number', 'id');
                    })
                    ->required(),
                Forms\Components\TextInput::make('number')
                    ->label('Тоот дугаар')
                    ->required()
                    ->numeric(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('number')->label('Тоот дугаар')->sortable(),
                Tables\Columns\TextColumn::make('davhar.number')->label('Давхар')->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
