<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\LiftGroupResource\Pages;
use App\Models\LiftGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LiftGroupResource extends Resource
{
    protected static ?string $model = LiftGroup::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'лифт групп';

    protected static ?string $pluralModelLabel = 'Лифтийн группүүд';

    protected static ?string $navigationGroup = 'Лифт удирдлага';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Нэр')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('orc_id')
                    ->label('Орц')
                    ->relationship('orc', 'number')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Нэр')
                    ->searchable(),
                Tables\Columns\TextColumn::make('orc.number')
                    ->label('Орц')
                    ->sortable(),
                Tables\Columns\TextColumn::make('devices_count')
                    ->label('Төхөөрөмжийн тоо')
                    ->counts('devices'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Үүсгэсэн')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Засагдсан')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLiftGroups::route('/'),
            'create' => Pages\CreateLiftGroup::route('/create'),
            'edit' => Pages\EditLiftGroup::route('/{record}/edit'),
        ];
    }
}
