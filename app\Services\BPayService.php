<?php

namespace App\Services;

use App\Exceptions\SystemException;
use App\Models\BpayToken;
use App\Models\Constant\ConstData;
use Illuminate\Support\Facades\Http;

class BPayService
{
    public function createBPayToken($orshinSuugchId)
    {
        $bpayHost = config('services.bpay.url');
        $username = config('services.bpay.user_name');
        $password = config('services.bpay.password');
        $param = ['username' => $username, 'password' => $password];
        $response = Http::withoutVerifying()->withOptions(['verify' => false])->post($bpayHost.'/users/api/v1/user/oauth/token', $param);
        if ($response->successful()) {
            if (! isset($response['responseCode']) || ! $response['responseCode']) {
                throw new SystemException(ConstData::BPAY_EXCEPTION, 1, json_decode($response));
            }
            $data = $response['data'];
            BpayToken::where(BpayToken::ORSHIN_SUUGCH_ID, $orshinSuugchId)->delete();
            $bPayToken = BpayToken::create([
                BpayToken::USER_ID => $data['userId'],
                BpayToken::ORSHIN_SUUGCH_ID => $orshinSuugchId,
                BpayToken::USER_NAME => $data['username'],
                BpayToken::TRANSACTION_METHOD_ID => $data['transactionMethodId'],
                BpayToken::TOKEN_TYPE => $data['tokenType'],
                BpayToken::ROLE_ID => $data['roleId'],
                BpayToken::REFRESH_TOKEN => $data['refreshToken'],
                BpayToken::ACCESS_TOKEN => $data['accessToken'],
                BpayToken::EXPIRES_IN => $data['expiresIn'],
                BpayToken::JTI => $data['jti'],
            ]);
        }
        if (! isset($bPayToken)) {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 1);
        }

        return $bPayToken;
    }

    public function checkBpayUser($orshinSuugch)
    {
        $response = $this->callApiWithToken($orshinSuugch, 'payment/api/v1/customer/check', ConstData::GET, ['userId' => $orshinSuugch->uniq_code]);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function createBpayUser($orshinSuugch)
    {
        $ourEmail = config('services.bpay.our_email');
        $param = ['userId' => $orshinSuugch->uniq_code, 'email' => $ourEmail];
        $response = $this->callApiWithToken($orshinSuugch, 'payment/api/v1/customer/register', ConstData::POST, $param);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function getInvoicesByCID($orshinSuugch, $cid)
    {
        $response = $this->callApiWithTokenAndUniqCode($orshinSuugch, 'search/api/v1/Search/FindCid', ConstData::GET, ['Cid' => $cid]);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function createInvoice($orshinSuugch, $billIds)
    {
        $response = $this->callApiWithTokenAndUniqCode($orshinSuugch, 'payment/api/v1/Invoice/Create', ConstData::POST, ['billIds' => $billIds]);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function createTransaction($orshinSuugch, $isOrg, $vatInfo, $invoiceId)
    {
        $param = ['invoiceId' => $invoiceId, 'isOrg' => $isOrg, 'vatInfo' => $vatInfo];
        $response = $this->callApiWithTokenAndUniqCode($orshinSuugch, 'payment/api/v1/invoice/transaction/create', ConstData::POST, $param);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function checkInvoice($orshinSuugch, $invoiceId)
    {
        $response = $this->callApiWithTokenAndUniqCode($orshinSuugch, 'payment/api/v1/merchant/bill/check/'.$invoiceId, ConstData::GET, []);
        if (isset($response) && isset($response->responseCode) && $response->responseCode) {
            return $response->data;
        } else {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, $response);
        }
    }

    public function callApiWithToken($orshinSuugch, $apiName, $methodType, $param)
    {
        $bpayHost = config('services.bpay.url');
        $url = "$bpayHost/$apiName";
        $bpayToken = BpayToken::where(BpayToken::ORSHIN_SUUGCH_ID, $orshinSuugch->id)->orderBy(BpayToken::CREATED_AT, 'desc')->first();
        if (! isset($bpayToken)) {
            $bpayToken = $this->createBPayToken($orshinSuugch->id);
        }
        $response = $this->callApi($orshinSuugch->uniq_code, $methodType, $bpayToken->access_token, $url, $param);
        if (isset($response->responseCode) && ! $response->responseCode) {
            $response = $this->callApi($orshinSuugch->uniq_code, $methodType, $bpayToken->refresh_token, $url, $param);
        }

        return $response;
    }

    public function callApiWithTokenAndUniqCode($orshinSuugch, $apiName, $methodType, $param)
    {
        if (! isset($orshinSuugch->uniq_code)) {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 7);
        }

        return $this->callApiWithToken($orshinSuugch, $apiName, $methodType, $param);
    }

    public function callApi($uniqCode, $methodType, $token, $url, $param)
    {
        switch ($methodType) {
            case ConstData::GET:
                $response = Http::withToken($token)->withHeaders(['userId' => $uniqCode])->get($url, $param);
                break;
            case ConstData::POST:
                $response = Http::withToken($token)->withHeaders(['userId' => $uniqCode])->post($url, $param);
                break;
            default:
                throw new SystemException(ConstData::BPAY_EXCEPTION, 6);
        }
        if (! $response->successful()) {
            throw new SystemException(ConstData::BPAY_EXCEPTION, 5, json_decode($response));
        }

        return json_decode($response);
    }
}
