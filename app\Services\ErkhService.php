<?php

namespace App\Services;

use App\Enums\ProductEnum;
use App\Exceptions\SystemException;
use App\Http\Tools\DateTool;
use App\Jobs\RemoveDeviceCode;
use App\Models\Constant\ConstData;
use App\Models\Erkh;
use App\Models\Invoice;
use App\Models\Package;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class ErkhService
{
    public function __construct() {}

    public function getCurrentErkhs($orshinSuugchId, $korpusId, $number)
    {
        $nowDate = DateTool::nowDate();
        $erkhs = Erkh::where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(Erkh::KORPUS_ID, $korpusId)
            ->where(Erkh::NUMBER, $number)
            ->where(function (Builder $query) use ($nowDate) {
                $query->where(function (Builder $query) use ($nowDate) {
                    $query->where(Erkh::BEGIN_DATE, '<=', $nowDate)
                        ->where(Erkh::END_DATE, '>=', $nowDate);
                })->orWhere(function (Builder $query) use ($nowDate) {
                    $query->where(Erkh::BEGIN_DATE, '>', $nowDate);
                });
            })->get();

        return $erkhs;
    }

    public function getCurrentProductErkhs($orshinSuugchId, $korpusId, $number, $product)
    {
        $nowDate = DateTool::nowDate();
        $erkhs = Erkh::with(Erkh::RELATION_INVOICE)
            ->where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(Erkh::KORPUS_ID, $korpusId)
            ->where(Erkh::NUMBER, $number)
            ->where(Erkh::PRODUCTS, 'LIKE', "%$product%")
            ->where(function (Builder $query) use ($nowDate) {
                $query->where(function (Builder $query) use ($nowDate) {
                    $query->where(Erkh::BEGIN_DATE, '<=', $nowDate)
                        ->where(Erkh::END_DATE, '>=', $nowDate);
                })->orWhere(function (Builder $query) use ($nowDate) {
                    $query->where(Erkh::BEGIN_DATE, '>', $nowDate);
                });
            })->get();

        return $erkhs;
    }

    public function getCurrentErkh($orshinSuugchId, $korpusId, $number, $product)
    {
        $nowDate = DateTool::nowDate();
        $erkh = Erkh::where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(Erkh::PRODUCTS, 'LIKE', "%$product%")
            ->where(Erkh::KORPUS_ID, $korpusId)
            ->where(Erkh::NUMBER, $number)
            ->where(Erkh::BEGIN_DATE, '<=', $nowDate)
            ->where(Erkh::END_DATE, '>=', $nowDate)
            ->first();

        return $erkh;
    }

    public function getCurrentGateErkh($orshinSuugchId, $korpusId, $number)
    {
        return $this->getCurrentErkh($orshinSuugchId, $korpusId, $number, (ProductEnum::GATE)->value);
    }

    public function hasAnyErkh($orshinSuugchId)
    {
        $erkh = Erkh::where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)->first();

        return isset($erkh);
    }

    public function hasErkhByBairOrc($orshinSuugchId, $korpusId, $number)
    {
        $erkh = Erkh::where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)->where(Erkh::KORPUS_ID, $korpusId)->where(Erkh::NUMBER, $number)->first();

        return isset($erkh);
    }

    public function hasUsedPackage($orshinSuugchId, $korpusId, $number, $packageId)
    {
        $erkh = Erkh::where(Erkh::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(Erkh::KORPUS_ID, $korpusId)
            ->where(Erkh::NUMBER, $number)
            ->whereHas(ERKH::RELATION_INVOICE, function (Builder $query) use ($packageId) {
                $query->where(Invoice::PACKAGE_ID, $packageId);
            })->first();

        return isset($erkh);
    }

    public function setFreeErkh($orshinSuugchId, $korpusId, $number, $packageId)
    {
        $hasUsedPackage = $this->hasUsedPackage($orshinSuugchId, $korpusId, $number, $packageId);
        if ($hasUsedPackage) {
            throw new SystemException(ConstData::ERKH_EXCEPTION, 1);
        }

        $package = Package::find($packageId);
        $newInvoice = Invoice::create([
            Invoice::INVOICE_CODE => '',
            Invoice::SENDER_INVOICE_NO => '',
            Invoice::INVOICE_RECEIVER_CODE => '',
            Invoice::INVOICE_DESCRIPTION => '',
            Invoice::ORSHIN_SUUGCH_ID => $orshinSuugchId,
            Invoice::KORPUS_ID => $korpusId,
            Invoice::NUMBER => $number,
            Invoice::PACKAGE_ID => $package->id,
            Invoice::PACKAGE_NAME => $package->name,
            Invoice::PACKAGE_PRODUCTS => $package->products,
            Invoice::AMOUNT => 0,
            Invoice::STATUS => ConstData::INVOICE_STATUS_COMPLETED,
            Invoice::INVOICE_CODE => '',
            Invoice::SENDER_INVOICE_NO => '',
            Invoice::INVOICE_RECEIVER_CODE => '',
            Invoice::INVOICE_DESCRIPTION => '',
            Invoice::QR_TEXT => '',
            Invoice::QR_IMAGE => '',
            Invoice::QPAY_SHORTURL => '',
            Invoice::URLS => [],
            Invoice::VALID_DAY => $package->valid_day,
        ]);
        $this->setErkh($orshinSuugchId, $korpusId, $number, $newInvoice->id, $packageId, $package->valid_day);

        return $newInvoice;
    }

    public function setErkh($orshinSuugchId, $korpusId, $number, $invoiceId, $packageId, $validDay)
    {
        $package = Package::find($packageId);
        $newErkh = new Erkh([
            Erkh::ORSHIN_SUUGCH_ID => $orshinSuugchId,
            Erkh::KORPUS_ID => $korpusId,
            Erkh::NUMBER => $number,
            Erkh::INVOICE_ID => $invoiceId,
            Erkh::BEGIN_DATE => Carbon::now(),
            Erkh::END_DATE => Carbon::now()->addDays($validDay),
            Erkh::PRODUCTS => $package->products,
        ]);
        $newErkh->save();
    }

    public function removeDeviceCodes()
    {
        $erkhs = Erkh::where(Erkh::PRODUCTS, 'LIKE', '%'.(ProductEnum::KEY)->value.'%')->where(Erkh::REMOVED_DEVICE_CODE, false)->get();
        $deviceService = resolve(DeviceService::class);
        foreach ($erkhs as $erkh) {
            $devEui = $deviceService->getDevEuiOs($erkh->orshin_suugch_id, $erkh->korpus_id, $erkh->orc_id);
            RemoveDeviceCode::dispatch($devEui, $erkh->orshin_suugch->device_code, $erkh->id);
        }
    }
}
