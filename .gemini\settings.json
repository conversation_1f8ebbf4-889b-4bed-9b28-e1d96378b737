{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "cvsecurity_postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "****************************************************/biosecurity-boot"]}, "cozy_postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://cozy:123456@127.0.0.1:5432/cozy"]}}}