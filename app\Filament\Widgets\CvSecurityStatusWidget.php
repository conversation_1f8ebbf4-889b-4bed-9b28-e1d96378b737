<?php

namespace App\Filament\Widgets;

use App\Services\CvSecurityService\CvSecurityService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CvSecurityStatusWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $stats = [];

        try {
            $cvSecurityService = resolve(CvSecurityService::class);
            $status = $cvSecurityService->getServiceStatus();

            $color = $status['status'] === 'online' ? 'success' : 'danger';
            $icon = $status['status'] === 'online' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
            $description = $status['host'].':'.$status['port'];

            $stats[] = Stat::make('CV Security', ucfirst($status['status']))
                ->description($description)
                ->descriptionIcon($icon)
                ->color($color);
        } catch (\Exception $e) {
            $stats[] = Stat::make('CV Security', 'Error')
                ->description('Service check failed')
                ->descriptionIcon('heroicon-o-exclamation-triangle')
                ->color('danger');
        }

        try {
            $cvSecurityServiceExt = resolve(CvSecurityServiceExt::class);
            $statusExt = $cvSecurityServiceExt->getServiceStatus();

            $colorExt = $statusExt['status'] === 'online' ? 'success' : 'danger';
            $iconExt = $statusExt['status'] === 'online' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
            $descriptionExt = $statusExt['host'].':'.$statusExt['port'];

            $stats[] = Stat::make('CV Security EXT', ucfirst($statusExt['status']))
                ->description($descriptionExt)
                ->descriptionIcon($iconExt)
                ->color($colorExt);
        } catch (\Exception $e) {
            $stats[] = Stat::make('CV Security EXT', 'Error')
                ->description('Service check failed')
                ->descriptionIcon('heroicon-o-exclamation-triangle')
                ->color('danger');
        }

        return $stats;
    }
}
