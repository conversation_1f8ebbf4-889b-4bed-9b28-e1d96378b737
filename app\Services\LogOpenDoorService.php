<?php

namespace App\Services;

use App\Models\Constant\ConstData;
use App\Models\LogOpenDoor;

class LogOpenDoorService
{
    public function createLogOpenDoor($orshinSuugchId, $devEui, $product)
    {
        $toolService = resolve(ToolService::class);
        $logNo = $toolService->generateUniqId(6);

        $logOpenDoor = LogOpenDoor::create([
            LogOpenDoor::ORSHIN_SUUGCH_ID => $orshinSuugchId,
            LogOpenDoor::DEV_EUI => $devEui,
            LogOpenDoor::LOG_NO => $logNo,
            LogOpenDoor::PRODUCT => $product,
            LogOpenDoor::STATUS => ConstData::LOG_OPEN_DOOR_STATUS_PENDING,
        ]);

        return $logOpenDoor;
    }
}
