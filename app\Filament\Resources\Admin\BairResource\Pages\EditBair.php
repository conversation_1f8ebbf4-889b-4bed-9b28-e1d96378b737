<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Exceptions\DeleteProtectionException;
use App\Filament\Resources\Admin\BairResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditBair extends EditRecord
{
    protected static string $resource = BairResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function () {
                    try {
                        $this->record->delete();

                        return redirect()->to($this->getResource()::getUrl('index'));
                    } catch (DeleteProtectionException $e) {
                        Notification::make()
                            ->title('Delete Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
