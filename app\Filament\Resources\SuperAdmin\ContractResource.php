<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\ContractResource\Pages;
use App\Models\Constant\ConstData;
use App\Models\Contract;
use App\Models\ContractTemplate;
use App\Models\Sukh;
use App\Models\Zuuchlagch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ContractResource extends Resource
{
    protected static ?string $model = Contract::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Гэрээ';

    protected static ?string $modelLabel = 'гэрээ';

    protected static ?int $navigationSort = 6;

    protected static ?string $slug = 'contracts';

    protected static ?string $navigationGroup = 'Гэрээ';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Contract::SUKH_ID)
                            ->label('Сөх')
                            ->options(Sukh::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make(Contract::CONTRACT_TEMPLATE_ID)
                            ->label('Гэрээний загвар')
                            ->options(ContractTemplate::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->required(),

                        Forms\Components\DatePicker::make(Contract::BEGIN_DATE)->native(false)->displayFormat('d/m/Y'),
                        Forms\Components\DatePicker::make(Contract::END_DATE)->native(false)->displayFormat('d/m/Y'),

                        Forms\Components\TextInput::make(Contract::CONTRACT_NO)
                            ->label('Гэрээний дугаар')
                            ->maxValue(10)
                            ->required(),

                        Forms\Components\TextInput::make(Contract::AMOUNT)
                            ->label('Үнэ')
                            ->default(0)
                            ->numeric()
                            ->inputMode('decimal')
                            ->maxValue(999999999999999999999999),

                        Forms\Components\TextInput::make(Contract::KHARILTSAGCH_POSITION)
                            ->label('Харилцагчийн албан тушаал')
                            ->maxValue(50)
                            ->required(),

                        Forms\Components\TextInput::make(Contract::KHARILTSAGCH_SIGNA)
                            ->label('Харилцагчийн нэр')
                            ->maxValue(50)
                            ->required(),

                        Forms\Components\Select::make(Contract::ZUUCHLAGCH_ID)
                            ->label('Зуучлагч')
                            ->options(Zuuchlagch::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable(),

                        Forms\Components\FileUpload::make(Contract::FILE_PATH)
                            ->disk('minio')
                            ->visibility('private')
                            ->removeUploadedFileButtonPosition('right')
                            ->disabled()
                            ->downloadable()
                            ->preserveFilenames()
                            ->hidden(fn (?Contract $record) => $record === null),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Contract $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Contract $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Contract $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Contract $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('sukh.name')->label('Сөх')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contract_template.name')->label('Гэрээний загвар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('begin_date')->label('Эхлэх огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('end_date')->label('Дуусах огноо')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contract_no')->label('Гэрээний дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('amount')->label('Дүн')->numeric(
                    decimalPlaces: 0,
                    decimalSeparator: '.',
                    thousandsSeparator: ',',
                )->sortable()->searchable(),
                Tables\Columns\TextColumn::make('khariltsagch_signa')->label('Харилцагч')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('zuuchlagch_signa')->label('Зуучлагч')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContracts::route('/'),
            'create' => Pages\CreateContract::route('/create'),
            'edit' => Pages\EditContract::route('/{record}/edit'),
        ];
    }
}
