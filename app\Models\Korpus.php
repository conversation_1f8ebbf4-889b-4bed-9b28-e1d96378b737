<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Korpus
 *
 * @property int $id
 * @property int $bair_id
 * @property string $name
 * @property int $order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Bair $bair
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Orc> $orcs
 * @property-read int|null $orcs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Davhar> $davhars
 * @property-read int|null $davhars_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus query()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Korpus extends Model
{
    use HasDeleteProtection;
    use HasFactory;

    const ID = 'id';

    const BAIR_ID = 'bair_id';

    const NAME = 'name';

    const ORDER = 'order';

    const CODE = 'code';

    // Auto-generation constants
    const AUTO_GENERATE = 'auto_generate';

    const NUMBERING_TYPE = 'numbering_type';

    const NUMBER_OF_FLOORS = 'number_of_floors';

    const DOORS_PER_FLOOR = 'doors_per_floor';

    const DIGIT_MULTIPLIER = 'digit_multiplier';

    const RELATION_BAIR = 'bair';

    const RELATION_ORCS = 'orcs';

    const RELATION_DAVHARS = 'davhars';

    const RELATION_TOOTS = 'toots';

    protected $fillable = [
        self::BAIR_ID,
        self::NAME,
        self::ORDER,
        self::CODE,
    ];

    public function bair()
    {
        return $this->belongsTo(Bair::class);
    }

    public function orcs(): HasMany
    {
        return $this->hasMany(Orc::class)->orderBy('order', 'asc');
    }

    public function davhars(): HasMany
    {
        return $this->hasMany(Davhar::class)->orderBy('number', 'asc');
    }

    public function toots(): HasMany
    {
        return $this->hasMany(Toot::class)->orderBy('number', 'asc');
    }

    /**
     * Get total doors for this Korpus.
     */
    public function getTotalDoorsAttribute(): int
    {
        return $this->toots()->count();
    }

    public static function getDeleteProtectionMessage(): string
    {
        return 'Cannot delete this korpus because it has davhars, orcs, or toots. Please delete all related records first.';
    }
}
