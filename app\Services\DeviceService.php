<?php

namespace App\Services;

use App\Models\DeviceDtl;
use App\Models\OrshinSuugchToot;

class DeviceService
{
    public function __construct() {}

    public function getDevEuiOs($orshinSuugchId, $korpusId, $number)
    {
        $orshinSuugchToot = OrshinSuugchToot::where(OrshinSuugchToot::ORSHIN_SUUGCH_ID, $orshinSuugchId)
            ->where(OrshinSuugchToot::KORPUS_ID, $korpusId)
            ->where(OrshinSuugchToot::NUMBER, $number)->first();
        if (! isset($orshinSuugchToot)) {
            return null;
        }

        $orc = resolve(OrcService::class)->getOrc($korpusId, $number);
        $deviceDtl = DeviceDtl::where(DeviceDtl::KORPUS_ID, $orc->korpus_id)->where(DeviceDtl::ORC_ID, $orc->id)->first();

        return isset($deviceDtl) ? $deviceDtl->dev_eui : null;
    }
}
