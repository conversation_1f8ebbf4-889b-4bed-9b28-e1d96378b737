<?php

namespace App\Filament\Resources\Admin\KorpusResource\Pages;

use App\Exceptions\DeleteProtectionException;
use App\Filament\Resources\Admin\KorpusResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditKorpus extends EditRecord
{
    protected static string $resource = KorpusResource::class;

    public function enableFloorGeneration(): void
    {
        $this->data['generate_floors'] = true;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function () {
                    try {
                        $this->record->delete();

                        return redirect()->to($this->getResource()::getUrl('index'));
                    } catch (DeleteProtectionException $e) {
                        Notification::make()
                            ->title('Delete Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                }),
        ];
    }

    public function getBreadcrumbs(): array
    {
        $record = $this->getRecord();

        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            url()->route('filament.admin.resources.bairs.edit', $record->bair_id) => $record->bair->name,
            '#' => "Блок: {$record->name}",
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
