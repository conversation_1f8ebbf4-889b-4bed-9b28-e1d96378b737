<?php

namespace App\Traits;

use App\Exceptions\DeleteProtectionException;
use Illuminate\Database\QueryException;

trait HasDeleteProtection
{
    public static function getDeleteProtectionMessage(): string
    {
        return 'This record cannot be deleted because it has related records. Please delete the related records first.';
    }

    public function delete()
    {
        try {
            return parent::delete();
        } catch (QueryException $e) {
            if ($e->getCode() === '23000' || $e->getCode() === '23503') { // Foreign key constraint violation
                throw new DeleteProtectionException(static::getDeleteProtectionMessage());
            }
            throw $e;
        }
    }
}
