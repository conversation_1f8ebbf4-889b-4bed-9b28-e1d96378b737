<?php

namespace App\Filament\Resources\Admin\LiftGroupResource\Pages;

use App\Filament\Resources\Admin\LiftGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLiftGroups extends ListRecords
{
    protected static string $resource = LiftGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
