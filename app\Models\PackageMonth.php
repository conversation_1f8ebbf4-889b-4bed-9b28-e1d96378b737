<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\PackageMonth
 *
 * @mixin IdeHelperPackageMonth
 *
 * @property int $id
 * @property int $package_id
 * @property int $value
 * @property float $discount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Package $package
 *
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth query()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth whereDiscount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth wherePackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMonth whereValue($value)
 *
 * @mixin \Eloquent
 */
class PackageMonth extends Model
{
    use HasFactory;

    const TABLE = 'package_months';

    const ID = 'id';

    const PACKAGE_ID = 'package_id';

    const VALUE = 'value';

    const DISCOUNT = 'discount';

    const RELATION_PACKAGE = 'package';

    protected $fillable = [
        self::ID,
        self::PACKAGE_ID,
        self::VALUE,
        self::DISCOUNT,
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
