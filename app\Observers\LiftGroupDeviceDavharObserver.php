<?php

namespace App\Observers;

use App\Models\LiftGroupDeviceDavhar;
use App\Services\LiftGroupDeviceDavharSyncService;
use Illuminate\Support\Facades\Log;

class LiftGroupDeviceDavharObserver
{
    protected LiftGroupDeviceDavharSyncService $liftGroupDeviceDavharSyncService;

    public function __construct(LiftGroupDeviceDavharSyncService $liftGroupDeviceDavharSyncService)
    {
        $this->liftGroupDeviceDavharSyncService = $liftGroupDeviceDavharSyncService;
    }

    /**
     * Handle the LiftGroupDeviceDavhar "created" event.
     */
    public function created(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        Log::info('LiftGroupDeviceDavharObserver: LiftGroupDeviceDavhar created event triggered', [
            'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
            'lift_group_device_id' => $liftGroupDeviceDavhar->lift_group_device_id,
            'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
        ]);

        // Sync with CVSecurity service
        $this->liftGroupDeviceDavharSyncService->syncCreate($liftGroupDeviceDavhar);
    }

    /**
     * Handle the LiftGroupDeviceDavhar "updated" event.
     */
    public function updated(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($liftGroupDeviceDavhar->wasChanged(LiftGroupDeviceDavhar::CODE) && count($liftGroupDeviceDavhar->getChanges()) === 1) {
            Log::debug('LiftGroupDeviceDavharObserver: Skipping sync for code-only update', [
                'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
            ]);

            return;
        }

        Log::info('LiftGroupDeviceDavharObserver: LiftGroupDeviceDavhar updated event triggered', [
            'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
            'lift_group_device_id' => $liftGroupDeviceDavhar->lift_group_device_id,
            'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
            'changed_fields' => array_keys($liftGroupDeviceDavhar->getChanges()),
        ]);

        // Sync with CVSecurity service
        $this->liftGroupDeviceDavharSyncService->syncUpdate($liftGroupDeviceDavhar);
    }

    /**
     * Handle the LiftGroupDeviceDavhar "deleted" event.
     */
    public function deleted(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        Log::info('LiftGroupDeviceDavharObserver: LiftGroupDeviceDavhar deleted event triggered', [
            'lift_group_device_davhar_id' => $liftGroupDeviceDavhar->id,
            'lift_group_device_id' => $liftGroupDeviceDavhar->lift_group_device_id,
            'davhar_id' => $liftGroupDeviceDavhar->davhar_id,
            'cv_code' => $liftGroupDeviceDavhar->code,
        ]);

        // Sync with CVSecurity service
        $this->liftGroupDeviceDavharSyncService->syncDelete($liftGroupDeviceDavhar);
    }

    /**
     * Handle the LiftGroupDeviceDavhar "retrieved" event.
     * This can be used for read operations if needed.
     */
    public function retrieved(LiftGroupDeviceDavhar $liftGroupDeviceDavhar): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->liftGroupDeviceDavharSyncService->syncRead($liftGroupDeviceDavhar);
    }
}
