<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div
        x-data="{
            permissions: {},
            offsets: {}, // Back to davhar-device based: [davharId][deviceId] = offset
            devices: [],
            davhars: [],
            editingOffset: {}, // Track which offset is being edited: [davharId][deviceId] = true/false
            tempOffsets: {}, // Temporary offset values during editing: [davharId][deviceId] = value

            init() {
                console.log('DavharDeviceTable: init() called');
                this.devices = @js($field->getDevices());
                this.davhars = @js($field->getDavhars());

                console.log('DavharDeviceTable: Loaded davhars:', this.davhars);
                console.log('DavharDeviceTable: Loaded devices:', this.devices);

                // Store reference for inline event handlers
                window.davharComponent = this;

                // Initialize permissions and offsets from saved form data first, then CVSecurity
                this.initializeFromSavedData();

                // Watch for device changes
                this.$watch('$wire.data.device_ids', (newDeviceIds) => {
                    // Reload devices when device_ids change
                    if (newDeviceIds && newDeviceIds.length > 0) {
                        // Trigger a component refresh to get new device data
                        $wire.$refresh();
                    }
                });
            },
            
            initializeFromSavedData() {
                console.log('DavharDeviceTable: initializeFromSavedData() called');
                
                // First, get saved data from Livewire
                const savedPermissions = $wire.data.davhar_device_permissions || {};
                const savedOffsets = $wire.data.davhar_offsets || {}; // Back to davhar_offsets
                
                console.log('DavharDeviceTable: Saved permissions from Livewire:', savedPermissions);
                console.log('DavharDeviceTable: Saved offsets from Livewire:', savedOffsets);
                
                // Initialize with saved data if available
                this.permissions = JSON.parse(JSON.stringify(savedPermissions));
                this.offsets = JSON.parse(JSON.stringify(savedOffsets));
                
                // Initialize permissions and offsets based on CVSecurity data for any missing values
                this.initializeFromCvSecurity();
            },
            
            initializeFromCvSecurity() {
                console.log('DavharDeviceTable: initializeFromCvSecurity() called');
                
                // Initialize permissions, offsets, and original floors for all davhar-device combinations
                this.davhars.forEach(davhar => {
                    this.devices.forEach(device => {
                        if (!this.permissions[davhar.id]) this.permissions[davhar.id] = {};
                        if (!this.offsets[davhar.id]) this.offsets[davhar.id] = {};
                        
                        // Set permission based on CVSecurity matching ONLY if not already set by saved data
                        if (this.permissions[davhar.id][device.id] === undefined) {
                            this.permissions[davhar.id][device.id] = this.hasMatchingFloor(davhar.id, device.id);
                            console.log(`DavharDeviceTable: Set CVSecurity default permission for davhar ${davhar.id}, device ${device.id}:`, this.permissions[davhar.id][device.id]);
                        } else {
                            console.log(`DavharDeviceTable: Keeping saved permission for davhar ${davhar.id}, device ${device.id}:`, this.permissions[davhar.id][device.id]);
                        }
                        
                        // Set default offset ONLY if not already set by saved data
                        if (this.offsets[davhar.id][device.id] === undefined) {
                            this.offsets[davhar.id][device.id] = 0;
                            console.log(`DavharDeviceTable: Set default offset for davhar ${davhar.id}, device ${device.id}: 0`);
                        } else {
                            console.log(`DavharDeviceTable: Keeping saved offset for davhar ${davhar.id}, device ${device.id}:`, this.offsets[davhar.id][device.id]);
                        }
                        
                        // Original floors not needed - we'll show current floor info directly
                    });
                });
                
                // Sync with Livewire
                $wire.set('data.davhar_device_permissions', this.permissions);
                $wire.set('data.davhar_offsets', this.offsets); // Back to davhar_offsets
                
                console.log('DavharDeviceTable: Final permissions:', this.permissions);
                console.log('DavharDeviceTable: Final offsets:', this.offsets);
            },
            
            get davhars() {
                return @js($field->getDavhars());
            },
            
            get floorMatchingData() {
                return @js($field->getFloorMatchingData());
            },
            
            // Check if davhar-device combination has a matching CVSecurity floor
            hasMatchingFloor(davharId, deviceId) {
                const davhar = this.davhars.find(d => d.id == davharId);
                
                // Find matching floor in CVSecurity device - look for any floor that could match this davhar with any offset
                const matchingData = this.floorMatchingData[davharId] && this.floorMatchingData[davharId][deviceId];
                if (matchingData && matchingData.all_floors) {
                    // Check if there's any enabled floor that could potentially match this davhar
                    return matchingData.all_floors.some(floor => floor.enabled);
                }

                return false;
            },
            
            // Check if matching floor is enabled
            isFloorEnabled(davharId, deviceId) {
                const davhar = this.davhars.find(d => d.id == davharId);
                const offset = this.getOffset(davharId, deviceId);

                // Find matching floor in CVSecurity device using correct formula: CVSecurity floor_no + offset = davhar.number
                const matchingData = this.floorMatchingData[davharId] && this.floorMatchingData[davharId][deviceId];
                if (matchingData && matchingData.all_floors) {
                    const matchingFloor = matchingData.all_floors.find(floor => floor.floor_no + offset === davhar.number);
                    return matchingFloor ? matchingFloor.enabled : false;
                }

                return false;
            },
            
            // Get offset for davhar-device combination
            getOffset(davharId, deviceId) {
                return (this.offsets[davharId] && this.offsets[davharId][deviceId]) || 0;
            },
            
            // Get floor matching info for display
            getFloorInfo(davharId, deviceId) {
                const davhar = this.davhars.find(d => d.id == davharId);
                
                // Look through ALL davhar-device combinations to find which CVSecurity floor should appear on this davhar
                // Logic: if davhar A has offset X, then CVSecurity floor Y should appear on davhar (Y + X)
                // So for this davhar, we need to find if any CVSecurity floor + its offset = this davhar number
                
                for (let checkDavhar of this.davhars) {
                    const checkOffset = this.getOffset(checkDavhar.id, deviceId);
                    const matchingData = this.floorMatchingData[checkDavhar.id] && this.floorMatchingData[checkDavhar.id][deviceId];
                    
                    if (matchingData && matchingData.all_floors) {
                        // Find a CVSecurity floor that, when added to its offset, equals this davhar's number
                        const matchingFloor = matchingData.all_floors.find(floor =>
                            floor.floor_no + checkOffset === davhar.number
                        );

                        if (matchingFloor) {
                            return {
                                found: true,
                                enabled: matchingFloor.enabled,
                                name: matchingFloor.name,
                                floor_no: matchingFloor.floor_no,
                                originalDavharId: checkDavhar.id,
                                offset: checkOffset
                            };
                        }
                    }
                }

                return {
                    found: false,
                    enabled: false,
                    name: null,
                    floor_no: null
                };
            },
            
            
            getFloorStatusClass(davharId, deviceId) {
                const floorInfo = this.getFloorInfo(davharId, deviceId);
                if (floorInfo.found) {
                    return floorInfo.enabled ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400';
                }
                return 'text-red-500 dark:text-red-400';
            },


            
            updateAllPermissions() {
                // Recalculate all permissions based on current offsets
                this.davhars.forEach(davhar => {
                    this.devices.forEach(device => {
                        if (!this.permissions[davhar.id]) this.permissions[davhar.id] = {};
                        
                        // Only enable if there's a matching enabled floor
                        const floorInfo = this.getFloorInfo(davhar.id, device.id);
                        if (!floorInfo.found || !floorInfo.enabled) {
                            this.permissions[davhar.id][device.id] = false;
                        }
                        // Keep existing permission if there are matching enabled floors
                    });
                });
                $wire.set('data.davhar_device_permissions', this.permissions);
            },
            
            checkAll() {
                this.davhars.forEach(davhar => {
                    this.devices.forEach(device => {
                        if (!this.permissions[davhar.id]) this.permissions[davhar.id] = {};
                        // Only check if there's a matching enabled floor
                        const floorInfo = this.getFloorInfo(davhar.id, device.id);
                        if (floorInfo.found && floorInfo.enabled) {
                            this.permissions[davhar.id][device.id] = true;
                        }
                    });
                });
                $wire.set('data.davhar_device_permissions', this.permissions);
                console.log('All checked:', this.permissions);
            },
            
            uncheckAll() {
                this.permissions = {};
                $wire.set('data.davhar_device_permissions', this.permissions);
                console.log('All unchecked');
            },

            // Start editing offset
            startEditingOffset(davharId, deviceId) {
                if (!this.editingOffset[davharId]) this.editingOffset[davharId] = {};
                if (!this.tempOffsets[davharId]) this.tempOffsets[davharId] = {};

                this.editingOffset[davharId][deviceId] = true;
                this.tempOffsets[davharId][deviceId] = this.getOffset(davharId, deviceId);
            },

            // Cancel editing offset
            cancelEditingOffset(davharId, deviceId) {
                if (this.editingOffset[davharId]) {
                    this.editingOffset[davharId][deviceId] = false;
                }
                if (this.tempOffsets[davharId]) {
                    delete this.tempOffsets[davharId][deviceId];
                }
            },

            // Save offset changes
            saveOffset(davharId, deviceId) {
                const newOffset = this.tempOffsets[davharId] && this.tempOffsets[davharId][deviceId] !== undefined
                    ? this.tempOffsets[davharId][deviceId]
                    : this.getOffset(davharId, deviceId);

                if (!this.offsets[davharId]) this.offsets[davharId] = {};
                this.offsets[davharId][deviceId] = parseInt(newOffset) || 0;

                // Update permissions and save
                this.updateAllPermissions();
                $wire.set('data.davhar_offsets', this.offsets);

                // Stop editing
                this.cancelEditingOffset(davharId, deviceId);

                console.log('Offset saved:', davharId, deviceId, newOffset);
            },

            // Check if offset is being edited
            isEditingOffset(davharId, deviceId) {
                return this.editingOffset[davharId] && this.editingOffset[davharId][deviceId];
            },

            // Get temporary offset value during editing
            getTempOffset(davharId, deviceId) {
                return this.tempOffsets[davharId] && this.tempOffsets[davharId][deviceId] !== undefined
                    ? this.tempOffsets[davharId][deviceId]
                    : this.getOffset(davharId, deviceId);
            }
        }"
        class="fi-fo-field-wrp"
    >
        <div class="fi-ta overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
            <!-- No devices message -->
            <div x-show="devices.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
                <p>Төхөөрөмж сонгоно уу</p>
            </div>

            <!-- Check All / Uncheck All buttons -->
            <div x-show="devices.length > 0" class="bg-gray-50 dark:bg-white/5 px-4 py-2 border-b border-gray-200 dark:border-white/5">
                <div class="flex gap-2">
                    <button 
                        type="button"
                        @click="checkAll()"
                        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md transition-colors"
                    >
                        Check All
                    </button>
                    <button 
                        type="button"
                        @click="uncheckAll()"
                        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 border border-gray-300 rounded-md transition-colors dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                    >
                        Uncheck All
                    </button>
                </div>
            </div>
            
            <!-- Main Table -->
            <div x-show="devices.length > 0" class="overflow-x-auto">
                <table class="fi-ta-table w-full table-auto divide-y divide-gray-200 dark:divide-white/5">
                    <thead class="divide-y divide-gray-200 dark:divide-white/5">
                        <!-- Single header row -->
                        <tr class="bg-gray-50 dark:bg-white/5">
                            <th class="fi-ta-header-cell px-3 py-3.5 text-left border-r border-gray-300 dark:border-white/10">
                                <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                    Давхар
                                </span>
                            </th>
                            <template x-for="device in devices" :key="device.id">
                                <th class="fi-ta-header-cell px-3 py-3.5 text-center border-l border-gray-300 dark:border-white/10">
                                    <span x-text="device.name" class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white"></span>
                                </th>
                            </template>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-white/5">
                        <template x-for="davhar in davhars" :key="davhar.id">
                            <tr class="fi-ta-row [@media(hover:hover)]:transition [@media(hover:hover)]:duration-75 hover:bg-gray-50 dark:hover:bg-white/5">
                                <!-- Davhar name column -->
                                <td class="fi-ta-cell px-3 py-3 border-r border-gray-300 dark:border-white/10">
                                    <div class="text-sm font-medium text-gray-950 dark:text-white" x-text="`${davhar.name} [${davhar.number}]`">
                                    </div>
                                </td>
                                
                                <!-- Device columns - one column per device -->
                                <template x-for="device in devices" :key="`device-${davhar.id}-${device.id}`">
                                    <td class="fi-ta-cell px-3 py-3 text-center border-l border-gray-300 dark:border-white/10">
                                        <div class="flex flex-col items-center space-y-2">
                                            <!-- Enabled checkbox -->
                                            <template x-if="getFloorInfo(davhar.id, device.id).found && getFloorInfo(davhar.id, device.id).originalDavharId == davhar.id">
                                                <input
                                                    type="checkbox"
                                                    :checked="permissions[davhar.id] && permissions[davhar.id][device.id]"
                                                    @change="
                                                        if (!permissions[davhar.id]) permissions[davhar.id] = {};
                                                        permissions[davhar.id][device.id] = $event.target.checked;
                                                        $wire.set('data.davhar_device_permissions', permissions);
                                                    "
                                                    :disabled="!getFloorInfo(davhar.id, device.id).enabled"
                                                    class="fi-checkbox-input rounded border-none bg-white shadow-sm ring-1 ring-inset ring-gray-950/10 checked:ring-0 focus:ring-2 focus:ring-primary-600 focus:ring-offset-0 disabled:bg-gray-50 disabled:text-gray-50 disabled:checked:bg-current disabled:checked:text-gray-400 dark:bg-white/5 dark:ring-white/20 dark:checked:bg-primary-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:ring-white/10 dark:disabled:checked:ring-white/20 text-primary-600 dark:text-primary-500"
                                                />
                                            </template>

                                            <!-- Floor name -->
                                            <template x-if="getFloorInfo(davhar.id, device.id).found">
                                                <div :class="getFloorStatusClass(davhar.id, device.id)" class="text-xs font-medium">
                                                    <span x-text="`${getFloorInfo(davhar.id, device.id).name} [${getFloorInfo(davhar.id, device.id).floor_no}]`"></span>
                                                </div>
                                            </template>

                                            <!-- Offset button/input -->
                                            <template x-if="getFloorInfo(davhar.id, device.id).found && getFloorInfo(davhar.id, device.id).originalDavharId == davhar.id">
                                                <div class="flex items-center space-x-1">
                                                    <!-- Show button when not editing -->
                                                    <template x-if="!isEditingOffset(davhar.id, device.id)">
                                                        <button
                                                            @click="startEditingOffset(davhar.id, device.id)"
                                                            class="px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded border border-blue-300 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-200 dark:border-blue-700"
                                                            x-text="`Offset: ${getOffset(davhar.id, device.id)}`"
                                                        ></button>
                                                    </template>

                                                    <!-- Show input when editing -->
                                                    <template x-if="isEditingOffset(davhar.id, device.id)">
                                                        <div class="flex items-center space-x-1">
                                                            <input
                                                                type="number"
                                                                :value="getTempOffset(davhar.id, device.id)"
                                                                @input="
                                                                    if (!tempOffsets[davhar.id]) tempOffsets[davhar.id] = {};
                                                                    tempOffsets[davhar.id][device.id] = parseInt($event.target.value) || 0;
                                                                "
                                                                class="w-16 text-center text-xs border border-gray-300 rounded px-1 py-1 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                                                min="-10"
                                                                max="10"
                                                                step="1"
                                                            />
                                                            <button
                                                                @click="saveOffset(davhar.id, device.id)"
                                                                class="px-2 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded border border-green-300 dark:bg-green-900 dark:hover:bg-green-800 dark:text-green-200 dark:border-green-700"
                                                            >
                                                                Change
                                                            </button>
                                                            <button
                                                                @click="cancelEditingOffset(davhar.id, device.id)"
                                                                class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 rounded border border-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 dark:border-gray-600"
                                                            >
                                                                ✕
                                                            </button>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
                                        </div>
                                    </td>
                                </template>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
        <div x-show="devices.length === 0" class="fi-ta-empty-state-content mx-auto grid max-w-lg justify-items-center text-center">
            <div class="fi-ta-empty-state-description text-sm text-gray-500 dark:text-gray-400 py-8">
                Төхөөрөмж сонгосны дараа давхрын зөвшөөрөл тохируулах боломжтой
            </div>
        </div>
    </div>
</x-dynamic-component>