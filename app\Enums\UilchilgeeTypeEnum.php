<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UilchilgeeTypeEnum: string implements HasLabel
{
    case BUHEL = 'buhel';
    case M2 = 'm2';

    public static function getValues(): array
    {
        return ['buhel', 'm2'];
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::BUHEL => 'Бүхэл',
            self::M2 => 'Метр квадрат',
        };
    }
}
