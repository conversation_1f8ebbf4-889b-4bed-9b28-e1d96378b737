<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\PackageMonthResource\Pages;
use App\Models\Constant\ConstData;
use App\Models\Package;
use App\Models\PackageMonth;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;

class PackageMonthResource extends Resource
{
    protected static ?string $model = PackageMonth::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Багц сар';

    protected static ?string $modelLabel = 'багц сар';

    protected static ?int $navigationSort = 99;

    protected static ?string $slug = 'packageMonths';

    protected static ?string $navigationGroup = 'Үнэ тохиргоо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(PackageMonth::PACKAGE_ID)
                            ->label('Багц')
                            ->options(Package::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->required(),

                        Forms\Components\TextInput::make(PackageMonth::VALUE)
                            ->label('Сарын тоо')
                            ->numeric()
                            ->inputMode('decimal')
                            ->default(2)
                            ->minValue(2)
                            ->maxValue(240),

                        Forms\Components\TextInput::make(PackageMonth::DISCOUNT)
                            ->label('Хямдралын хувь')
                            ->default(1)
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(99.9)
                            ->suffix('%'),
                    ])
                    ->columns(1)
                    ->columnSpan(['lg' => fn (?PackageMonth $record) => $record === null ? 2 : 1]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (PackageMonth $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (PackageMonth $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?PackageMonth $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(PackageMonth::RELATION_PACKAGE.'.'.Package::NAME)->label('Багц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(PackageMonth::VALUE)->label('Сарын тоо')
                    ->icon('heroicon-m-calendar')
                    ->iconPosition(IconPosition::After)->sortable()->searchable(),
                Tables\Columns\TextColumn::make(PackageMonth::DISCOUNT)->label('Хямдралын хувь')
                    ->icon('heroicon-m-receipt-percent')
                    ->iconPosition(IconPosition::After)->sortable()->searchable(),
            ])
            ->defaultSort(PackageMonth::VALUE, 'asc')
            ->filters([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPackageMonths::route('/'),
            'create' => Pages\CreatePackageMonth::route('/create'),
            'edit' => Pages\EditPackageMonth::route('/{record}/edit'),
        ];
    }
}
