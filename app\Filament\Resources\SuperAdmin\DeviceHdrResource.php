<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\DeviceHdrResource\Pages;
use App\Filament\Resources\SuperAdmin\DeviceHdrResource\RelationManagers;
use App\Models\Constant\ConstData;
use App\Models\DeviceConfig;
use App\Models\DeviceHdr;
use App\Models\Sukh;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DeviceHdrResource extends Resource
{
    protected static ?string $model = DeviceHdr::class;

    protected static ?string $navigationIcon = 'heroicon-o-device-tablet';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Төхөөрөмж';

    protected static ?string $modelLabel = 'төхөөрөмж';

    protected static ?int $navigationSort = 5;

    protected static ?string $slug = 'devices';

    protected static ?string $navigationGroup = 'Тохиргоо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('sukh_id')
                    ->label('Сөх')
                    ->options(Sukh::all()->pluck(ConstData::NAME, ConstData::ID))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('device_config_id')
                    ->label('Төхөөрөмжийн тохиргоо')
                    ->options(DeviceConfig::all()->pluck(ConstData::NAME, ConstData::ID))
                    ->required(),

                Forms\Components\Textarea::make('description')->label('Тайлбар'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('sukh.name')->label('СӨХ')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('description')->label('Тайлбар')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_disabled')->label('Идэвхгүй эсэх')->sortable(),
                Tables\Columns\BooleanColumn::make('skip_fcnt_check')->label('skipFcntCheck')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DeviceDtlRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeviceHdrs::route('/'),
            'create' => Pages\CreateDeviceHdr::route('/create'),
            'edit' => Pages\EditDeviceHDr::route('/{record}/edit'),
        ];
    }
}
