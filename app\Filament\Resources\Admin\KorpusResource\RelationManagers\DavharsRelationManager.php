<?php

namespace App\Filament\Resources\Admin\KorpusResource\RelationManagers;

use App\Exceptions\DeleteProtectionException;
use App\Models\Davhar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class DavharsRelationManager extends RelationManager
{
    protected static string $relationship = 'davhars';

    protected static ?string $title = 'Давхрууд';

    protected static ?string $modelLabel = 'давхар';

    protected static ?string $pluralModelLabel = 'давхрууд';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(Davhar::NAME)
                    ->label('Давхрын нэр')
                    ->maxLength(30)
                    ->required(),

                Forms\Components\TextInput::make(Davhar::NUMBER)
                    ->label('Давхрын дугаар')
                    ->numeric()
                    ->minValue(-10)
                    ->maxValue(200)
                    ->default(0)
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make(Davhar::NAME)
                    ->label('Давхрын нэр')
                    ->sortable(),

                Tables\Columns\TextColumn::make(Davhar::NUMBER)
                    ->label('Давхрын дугаар')
                    ->sortable(),

                Tables\Columns\TextColumn::make('toots_count')
                    ->label('Тоотны тоо')
                    ->counts('toots'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function (Model $record) {
                        try {
                            $record->delete();
                        } catch (DeleteProtectionException $e) {
                            Notification::make()
                                ->title('Delete Failed')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();

                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $failedDeletions = 0;

                            foreach ($records as $record) {
                                try {
                                    $record->delete();
                                } catch (DeleteProtectionException $e) {
                                    $failedDeletions++;
                                }
                            }

                            if ($failedDeletions > 0) {
                                Notification::make()
                                    ->title('Some Deletions Failed')
                                    ->body("$failedDeletions record(s) could not be deleted because they have related toots.")
                                    ->danger()
                                    ->send();
                            }
                        }),
                ]),
            ]);
    }
}
