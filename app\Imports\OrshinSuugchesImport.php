<?php

namespace App\Imports;

use App\Services\OrshinSuugchService;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Row;

class OrshinSuugchesImport implements OnEachRow
{
    public function onRow(Row $row)
    {
        $rowIndex = $row->getIndex();
        $row = $row->toArray();

        if ($rowIndex == 1) {
            return;
        }

        $phone = $row[2];
        if (! preg_match('/^[0-9]{8}$/', $phone)) {
            return;
        }

        $osDatum = [
            'last_name' => $row[0],
            'name' => $row[1],
            'phone' => $phone,
            'email' => $row[3],
            'bair_name' => $row[4],
            'korpus_name' => $row[5],
            'number' => $row[6],
            'state_bank_code' => $row[7],
        ];
        $service = resolve(OrshinSuugchService::class);
        $service->importOs($osDatum);
    }
}
