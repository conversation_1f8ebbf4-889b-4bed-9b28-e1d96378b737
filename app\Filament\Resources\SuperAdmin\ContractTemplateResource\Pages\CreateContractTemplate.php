<?php

namespace App\Filament\Resources\SuperAdmin\ContractTemplateResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractTemplateResource;
use App\Models\ContractTemplate;
use App\Services\StorageService;
use Filament\Resources\Pages\CreateRecord;

class CreateContractTemplate extends CreateRecord
{
    protected static string $resource = ContractTemplateResource::class;

    protected function afterCreate(): void
    {
        $record = $this->record;
        $service = resolve(StorageService::class);
        $attachments = $service->putFilesToMinio(ContractTemplate::TABLE, $record, $this->data[ContractTemplate::ATTACHMENTS]);
        $this->data[ContractTemplate::ATTACHMENTS] = $attachments;
    }
}
