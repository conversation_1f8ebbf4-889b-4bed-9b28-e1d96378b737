<?php

namespace Database\Factories;

use App\Models\Davhar;
use Illuminate\Database\Eloquent\Factories\Factory;

class DavharFactory extends Factory
{
    protected $model = Davhar::class;

    public function definition(): array
    {
        return [
            'korpus_id' => \App\Models\Korpus::factory(),
            'name' => $this->faker->words(2, true), // Generate 2 words as floor name
            'number' => $this->faker->numberBetween(-10, 200), // Floor number from -10 to 200
        ];
    }
}
