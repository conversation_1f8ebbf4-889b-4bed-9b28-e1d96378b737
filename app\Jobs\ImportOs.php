<?php

namespace App\Jobs;

use App\Imports\OrshinSuugchesImport;
use App\Models\Constant\ConstData;
use App\Models\OrshinSuugchImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;

class ImportOs implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $id;

    public $filePath;

    /**
     * Create a new job instance.
     */
    public function __construct($id, $filePath)
    {
        $this->id = $id;
        $this->filePath = $filePath;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Excel::import(new OrshinSuugchesImport, $this->filePath);
        $osImport = OrshinSuugchImport::find($this->id);
        $osImport->status = ConstData::STATUS_SUCCESS;
        $osImport->message = ConstData::MSG_SUCCESS;
        $osImport->save();
    }
}
