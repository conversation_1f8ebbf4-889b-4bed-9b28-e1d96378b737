<?php

namespace App\Models;

use App\Traits\HasDeleteProtection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Orc
 *
 * @mixin IdeHelperOrc
 *
 * @property int $id
 * @property string $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LiftGroup> $liftGroups
 * @property-read int|null $lift_groups_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc query()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Orc extends Model
{
    use HasDeleteProtection;
    use HasFactory;

    const ID = 'id';

    const KORPUS_ID = 'korpus_id';

    const NUMBER = 'number';

    const CODE = 'code';

    const ORDER = 'order';

    const RELATION_KORPUS = 'korpus';

    const RELATION_TOOTS = 'toots';

    const RELATION_LIFT_GROUPS = 'liftGroups';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::CODE,
        self::ORDER,
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function toots(): HasMany
    {
        return $this->hasMany(Toot::class);
    }

    public function liftGroups(): HasMany
    {
        return $this->hasMany(LiftGroup::class);
    }

    public static function getDeleteProtectionMessage(): string
    {
        return 'Cannot delete this orc because it has lift groups or toots. Please delete all related records first.';
    }
}
