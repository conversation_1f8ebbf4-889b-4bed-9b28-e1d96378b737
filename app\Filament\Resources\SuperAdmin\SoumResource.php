<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\SoumResource\Pages;
use App\Models\Aimag;
use App\Models\Constant\ConstData;
use App\Models\Soum;
use App\Services\InfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SoumResource extends Resource
{
    protected static ?string $model = Soum::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel;

    protected static ?string $pluralModelLabel = 'Сум/Дүүрэг';

    protected static ?string $modelLabel = 'Сум/Дүүрэг';

    protected static ?int $navigationSort = 101;

    protected static ?string $slug = 'soums';

    protected static ?string $navigationGroup = 'Инфо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('aimag_id')
                            ->label('Аймаг/Хот')
                            ->options(Aimag::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->default(function () {
                                $service = resolve(InfoService::class);

                                return $service->getUBId();
                            })
                            ->searchable(),

                        Forms\Components\TextInput::make('name')
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Soum $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Soum $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Soum $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Soum $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('aimag.name')->label('Аймаг/Хот нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSoums::route('/'),
            'create' => Pages\CreateSoum::route('/create'),
            'edit' => Pages\EditSoum::route('/{record}/edit'),
        ];
    }
}
