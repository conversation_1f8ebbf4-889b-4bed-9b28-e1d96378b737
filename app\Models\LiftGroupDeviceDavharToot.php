<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LiftGroupDeviceDavharToot extends Model
{
    use HasFactory;

    protected $fillable = [
        'lift_group_davhar_id',
        'toot_id',
    ];

    public function liftGroupDeviceDavhar(): BelongsTo
    {
        return $this->belongsTo(LiftGroupDeviceDavhar::class);
    }

    public function toot(): BelongsTo
    {
        return $this->belongsTo(Toot::class);
    }
}
