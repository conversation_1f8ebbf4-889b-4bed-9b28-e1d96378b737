<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LiftGroupDevice extends Model
{
    use HasFactory;

    protected $table = 'lift_group_devices';

    protected $fillable = [
        'lift_group_id',
        'device_id',
    ];

    public function liftGroup(): BelongsTo
    {
        return $this->belongsTo(LiftGroup::class);
    }
}
