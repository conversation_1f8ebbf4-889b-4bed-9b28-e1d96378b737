<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BpayBill extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'bill_id' => $this->billId,
            'code' => $this->code,
            'bill_amount' => $this->billAmount,
            'total_amount' => $this->totalAmount,
            'paid_amount' => $this->paidAmount,
            'year' => $this->year,
            'month' => $this->month,
            'name' => $this->name,
            'org_type_id' => $this->orgTypeId,
            'org_name' => $this->orgName,
            'provider_id' => $this->providerId,
            'status_id' => $this->statusId,
        ];
    }
}
