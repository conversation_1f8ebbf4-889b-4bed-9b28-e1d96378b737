<?php

namespace App\Services;

use App\Enums\NehemjlehStatusEnum;
use App\Models\Bair;
use App\Models\Nehemjleh;
use App\Models\NehemjlehDtl;
use App\Models\OrshinSuugch;
use App\Models\OrshinSuugchToot;
use App\Models\Uilchilgee;
use Illuminate\Database\Eloquent\Builder;

class NehemjlehService
{
    public function createNehemjlehByTohirgoo($year, $month)
    {
        $nehemjlehTohirgoos = resolve(NehemjlehTohirgooService::class)->getNehemjlehTohirgoos();
        $nehemjlehCode = resolve(ToolService::class)->generateCodeFromDatetime();
        foreach ($nehemjlehTohirgoos as $key => $nehemjlehTohirgoo) {
            $uilchilgees = $nehemjlehTohirgoo->uilchilgees;
            $nehemjlehTohirgooDtls = $nehemjlehTohirgoo->nehemjleh_tohirgoo_dtls;
            $niitNuatguiDun = $uilchilgees->sum(Uilchilgee::PRICE);
            $nuatDun = 0;
            $niitDun = $niitNuatguiDun + $nuatDun;
            foreach ($nehemjlehTohirgooDtls as $key => $nehemjlehTohirgooDtl) {
                $bair = Bair::find($nehemjlehTohirgooDtl->bair_id);
                if (! isset($bair)) {
                    continue;
                }
                $nehemjleh = Nehemjleh::where(Nehemjleh::YEAR, $year)
                    ->where(Nehemjleh::MONTH, $month)
                    ->where(Nehemjleh::BAIR_ID, $nehemjlehTohirgooDtl->bair_id)
                    ->where(Nehemjleh::KORPUS_ID, $nehemjlehTohirgooDtl->korpus_id)
                    ->where(Nehemjleh::TOOT_NUMBER, $nehemjlehTohirgooDtl->number)->first();
                if (isset($nehemjleh)) {
                    continue;
                }

                $orshinSuugch = OrshinSuugch::where(OrshinSuugch::IS_ADMIN, true)
                    ->whereHas(OrshinSuugch::RELATION_ORSHIN_SUUGCH_TOOTS, function (Builder $query) use ($nehemjlehTohirgooDtl) {
                        return $query->where(OrshinSuugchToot::KORPUS_ID, $nehemjlehTohirgooDtl->korpus_id)
                            ->where(OrshinSuugchToot::NUMBER, $nehemjlehTohirgooDtl->number);
                    })->first();
                $newNehemjleh = Nehemjleh::create([
                    Nehemjleh::YEAR => $year,
                    Nehemjleh::MONTH => $month,
                    Nehemjleh::AIMAG_ID => $bair->aimag_id,
                    Nehemjleh::AIMAG_NAME => $bair->aimag->name,
                    Nehemjleh::SOUM_ID => $bair->soum_id,
                    Nehemjleh::SOUM_NAME => $bair->soum->name,
                    Nehemjleh::BAG_ID => $bair->bag_id,
                    Nehemjleh::BAG_NAME => $bair->bag->name,
                    Nehemjleh::SUKH_ID => $bair->sukh_id,
                    Nehemjleh::SUKH_NAME => $bair->sukh->name,
                    Nehemjleh::SUKH_REGISTRATION_NUMBER => $bair->sukh->registration_number,
                    Nehemjleh::BAIR_ID => $nehemjlehTohirgooDtl->bair_id,
                    Nehemjleh::BAIR_NAME => $nehemjlehTohirgooDtl->bair->name,
                    Nehemjleh::KORPUS_ID => $nehemjlehTohirgooDtl->korpus_id,
                    Nehemjleh::KORPUS_NAME => $nehemjlehTohirgooDtl->korpus->name,
                    Nehemjleh::TOOT_NUMBER => $nehemjlehTohirgooDtl->number,
                    Nehemjleh::ORSHIN_SUUGCH_ID => $orshinSuugch ? $orshinSuugch->id : null,
                    Nehemjleh::ORSHIN_SUUGCH_OVOG => $orshinSuugch ? $orshinSuugch->last_name : 'Тохируул',
                    Nehemjleh::ORSHIN_SUUGCH_NER => $orshinSuugch ? $orshinSuugch->name : 'Тохируул',
                    Nehemjleh::ORSHIN_SUUGCH_UNIQ_CODE => $orshinSuugch ? $orshinSuugch->uniq_code : null,
                    Nehemjleh::UTAS => $orshinSuugch ? $orshinSuugch->phone : 'Тохируул',
                    Nehemjleh::NIIT_DUN => $niitDun,
                    Nehemjleh::TULSUN_DUN => 0,
                    Nehemjleh::ULDEGDEL_DUN => $niitDun,
                    Nehemjleh::NIIT_NUATGUI_DUN => 0,
                    Nehemjleh::NUAT_DUN => $nuatDun,
                    Nehemjleh::STATUS => NehemjlehStatusEnum::TULUGDUUGUI,
                    Nehemjleh::CODE => $nehemjlehCode,
                ]);
                $nehemjlehCode++;

                foreach ($uilchilgees as $key => $uilchilgee) {
                    NehemjlehDtl::create([
                        NehemjlehDtl::NEHEMJLEH_ID => $newNehemjleh->id,
                        NehemjlehDtl::UILCHILGEE_NER => $uilchilgee->name,
                        NehemjlehDtl::DUN => $uilchilgee->price,
                    ]);
                }
            }
        }
    }
}
