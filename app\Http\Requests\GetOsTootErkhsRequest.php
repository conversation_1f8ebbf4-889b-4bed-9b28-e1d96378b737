<?php

namespace App\Http\Requests;

use App\Enums\ProductEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetOsTootErkhsRequest extends FormRequest
{
    const PARAMETER_ID = 'orshin_suugch_toot_id';

    const PARAMETER_PRODUCT = 'product';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_ID => 'required|exists:orshin_suugch_toots,id',
            self::PARAMETER_PRODUCT => [Rule::enum(ProductEnum::class)],
        ];
    }
}
